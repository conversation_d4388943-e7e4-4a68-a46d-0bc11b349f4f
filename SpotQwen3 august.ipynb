{"cells": [{"cell_type": "code", "execution_count": 19, "id": "3c181526", "metadata": {}, "outputs": [], "source": ["# Cell 1: Imports and Utilities\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import tifffile\n", "import random\n", "import cv2\n", "from scipy.ndimage import distance_transform_edt\n", "from skimage.measure import regionprops\n", "from skimage.morphology import skeletonize\n", "from scipy.spatial.distance import cdist\n", "from torch.utils.data import Dataset, DataLoader\n", "from sklearn.model_selection import train_test_split\n", "import albumentations as A\n", "import os\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "from skimage.color import label2rgb\n", "import glob as glob\n", "# Compatibility for AMP\n", "try:\n", "    from torch.amp import GradScaler, autocast\n", "except ImportError:\n", "    from torch.cuda.amp import GradScaler, autocast\n", "\n", "# --- Utility Functions ---\n", "def get_optimized_transforms():\n", "    \"\"\"Optimized augmentation pipeline for grayscale microscopy images.\"\"\"\n", "    return <PERSON><PERSON>([\n", "        <PERSON><PERSON>(p=0.5),\n", "        <PERSON><PERSON>(p=0.5),\n", "        <PERSON><PERSON>(limit=90, p=0.7),\n", "        <PERSON><PERSON>(blur_limit=(1, 3), sigma_limit=(0.1, 0.7), p=0.2),\n", "        <PERSON><PERSON>(blur_limit=3, p=0.1),\n", "        <PERSON><PERSON>ontrast(brightness_limit=0.15, contrast_limit=0.15, p=0.4),\n", "        A.Normalize(mean=0.0, std=1.0, max_pixel_value=1.0, p=1.0)\n", "    ], p=1.0)\n"]}, {"cell_type": "code", "execution_count": 20, "id": "afaf83d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SkeletonAwareSpotDataset class is ready.\n"]}], "source": ["### with improvements EDT from centroid and skeleton\n", "import torch\n", "import numpy as np\n", "import tifffile\n", "import random\n", "import cv2\n", "from torch.utils.data import Dataset\n", "from scipy.ndimage import distance_transform_edt\n", "from skimage.measure import regionprops\n", "from skimage.morphology import skeletonize\n", "from scipy.spatial.distance import cdist\n", "\n", "class SkeletonAwareSpotDataset(Dataset):\n", "    def __init__(self, image_paths, mask_paths, transform=None, patch_size=256):\n", "        assert len(image_paths) == len(mask_paths)\n", "        self.image_paths = image_paths\n", "        self.mask_paths = mask_paths\n", "        self.transform = transform\n", "        self.patch_size = patch_size\n", "\n", "    def _get_valid_spots(self, mask):\n", "        \"\"\"Get list of valid spot IDs (excluding background 0).\"\"\"\n", "        valid_ids = np.unique(mask)\n", "        return valid_ids[valid_ids > 0]\n", "\n", "    def _extract_spot_info(self, mask, valid_ids):\n", "        \"\"\"Extract centroids and individual spot masks.\"\"\"\n", "        centroids = []\n", "        spot_masks = []\n", "        for spot_id in valid_ids:\n", "            spot_mask = (mask == spot_id).astype(np.uint8)\n", "            props = regionprops(spot_mask)\n", "            if props:\n", "                centroids.append(props[0].centroid)\n", "                spot_masks.append(spot_mask)\n", "        return centroids, spot_masks\n", "\n", "    def _generate_semantic_mask(self, spot_masks, shape):\n", "        \"\"\"Combine individual spot masks into a single semantic mask.\"\"\"\n", "        semantic = np.zeros(shape, dtype=np.float32)\n", "        for mask in spot_masks:\n", "            semantic = np.maximum(semantic, mask.astype(np.float32))\n", "        return semantic\n", "\n", "    def _generate_skeleton_aware_distance_transform(self, spot_mask):\n", "        \"\"\"\n", "        Generate SDT, Skeleton, and Boundary maps for a single spot mask.\n", "        Corrected to calculate internal distance to skeleton properly.\n", "        \"\"\"\n", "        if not np.any(spot_mask):\n", "            return (np.zeros_like(spot_mask, dtype=np.float32),\n", "                    np.zeros_like(spot_mask, dtype=np.float32),\n", "                    np.zeros_like(spot_mask, dtype=np.float32))\n", "        \n", "        spot_area = np.sum(spot_mask)\n", "        H, W = spot_mask.shape\n", "        \n", "        # --- 1. Generate skeleton ---\n", "        skeleton = np.zeros_like(spot_mask, dtype=np.float32)\n", "        if spot_area <= 10:  # Small spots\n", "            props = regionprops(spot_mask.astype(np.uint8))\n", "            if props:\n", "                cy, cx = map(int, props[0].centroid)\n", "                # Use EDT from centroid instead of hardcoded gradient\n", "                Y, X = np.ogrid[:H, :W]\n", "                dist_from_center = np.sqrt((Y - cy)**2 + (X - cx)**2)\n", "                skeleton[spot_mask > 0] = 1.0 - np.clip(dist_from_center[spot_mask > 0] / 2.0, 0, 1)\n", "        else:  # Larger spots\n", "            try:\n", "                skel_temp = skeletonize(spot_mask > 0)\n", "                skeleton = skel_temp.astype(np.float32)\n", "                # Fallback if skeletonize produces an empty mask\n", "                if np.sum(skeleton) == 0:\n", "                    props = regionprops(spot_mask.astype(np.uint8))\n", "                    if props:\n", "                        cy, cx = map(int, props[0].centroid)\n", "                        if 0 <= cy < H and 0 <= cx < W:\n", "                            skeleton[cy, cx] = 1.0\n", "            except Exception:\n", "                # Final fallback to centroid\n", "                props = regionprops(spot_mask.astype(np.uint8))\n", "                if props:\n", "                    cy, cx = map(int, props[0].centroid)\n", "                    if 0 <= cy < H and 0 <= cx < W:\n", "                        skeleton[cy, cx] = 1.0\n", "        \n", "        # --- 2. Generate boundary ---\n", "        kernel = np.ones((3, 3), np.uint8)\n", "        eroded = cv2.erode(spot_mask.astype(np.uint8), kernel, iterations=1)\n", "        boundary = (spot_mask.astype(np.uint8) - eroded).astype(np.float32)\n", "        \n", "        # --- 3. Generate SDT (Corrected Logic) ---\n", "        spot_region = spot_mask > 0\n", "        # Distance to boundary (increases from boundary inward)\n", "        dist_to_boundary = distance_transform_edt(spot_mask)\n", "        # --- CRITICAL FIX: Internal Distance to Skeleton ---\n", "        skeleton_binary = skeleton > 0\n", "        if np.any(spot_region) and np.any(skeleton_binary):\n", "            # Get coordinates of spot pixels and skeleton pixels\n", "            coords_spot = np.argwhere(spot_region)\n", "            coords_skel = np.argwhere(skeleton_binary)\n", "            if len(coords_skel) > 0:\n", "                # Calculate distance from each spot pixel to the nearest skeleton pixel\n", "                dists = cdist(coords_spot, coords_skel, 'euclidean')\n", "                min_dists = np.min(dists, axis=1)\n", "                # Create internal distance map (same shape as spot_mask)\n", "                dist_to_skeleton_internal = np.full((H, W), np.inf, dtype=np.float32)\n", "                for (y, x), d in zip(coords_spot, min_dists):\n", "                    dist_to_skeleton_internal[y, x] = d\n", "                # Normalize distance to skeleton (0 at skeleton, increasing outward within spot)\n", "                max_skel_dist = np.max(min_dists) if len(min_dists) > 0 else 1.0\n", "                skeleton_norm = dist_to_skeleton_internal / (max_skel_dist + 1e-6)\n", "                skeleton_norm[~spot_region] = 0  # Ensure outside spot is 0\n", "            else:\n", "                skeleton_norm = np.zeros_like(spot_mask, dtype=np.float32)\n", "        else:\n", "            skeleton_norm = np.zeros_like(spot_mask, dtype=np.float32)\n", "        \n", "        # --- END CRITICAL FIX ---\n", "        # Normalize distance to boundary\n", "        if np.any(spot_region):\n", "            max_boundary_dist = np.max(dist_to_boundary[spot_region]) + 1e-6\n", "            boundary_norm = dist_to_boundary / max_boundary_dist\n", "            boundary_norm[~spot_region] = 0  # Ensure outside spot is 0\n", "            # SDT formula (aligning with paper intent)\n", "            alpha = 1.5 if spot_area <= 15 else 1.2\n", "            # SDT(x) = d_boundary(x) * (1 - d_skel(x))^α\n", "            # Where d_skel(x) is normalized internal distance (0 at skel, 1 far away)\n", "            sdt = boundary_norm * np.power(np.clip(1.0 - skeleton_norm, 0, 1), alpha)\n", "            sdt = np.clip(sdt, 0, 1)\n", "        else:\n", "            sdt = np.zeros_like(spot_mask, dtype=np.float32)\n", "        \n", "        return sdt, skeleton, boundary\n", "\n", "    def _generate_centroid_map(self, shape, centroids):\n", "        \"\"\"Generate a centroid heatmap with Gaussian peaks at spot centers.\"\"\"\n", "        centroid_map = np.zeros(shape, dtype=np.float32)\n", "        sigma = 1.0  # Controls the spread of the Gaussian\n", "        for cy, cx in centroids:\n", "            cx_int, cy_int = int(round(cx)), int(round(cy))\n", "            # Bounding box around the peak\n", "            x_min, x_max = max(0, cx_int - 6), min(shape[1], cx_int + 7)\n", "            y_min, y_max = max(0, cy_int - 6), min(shape[0], cy_int + 7)\n", "            Y, X = np.ogrid[y_min:y_max, x_min:x_max]\n", "            g = np.exp(-((X - cx)**2 + (Y - cy)**2) / (2 * sigma**2))\n", "            centroid_map[y_min:y_max, x_min:x_max] = np.maximum(\n", "                centroid_map[y_min:y_max, x_min:x_max], g\n", "            )\n", "        return centroid_map\n", "\n", "    def _generate_flow_field(self, semantic_mask, centroids):\n", "        \"\"\"\n", "        Vectorized flow field generation for all pixels within semantic regions.\n", "        Points from each pixel to its nearest centroid.\n", "        \"\"\"\n", "        h, w = semantic_mask.shape\n", "        if len(centroids) == 0:\n", "            return np.zeros((2, h, w), dtype=np.float32)\n", "        flow_field = np.zeros((2, h, w), dtype=np.float32)\n", "        # Get boolean mask for all spot pixels\n", "        spot_mask_flat = semantic_mask.flatten()\n", "        spot_indices = np.where(spot_mask_flat > 0)[0]\n", "        if len(spot_indices) == 0:\n", "            return flow_field\n", "        # Get coordinates of all spot pixels\n", "        y_coords, x_coords = np.unravel_index(spot_indices, (h, w))\n", "        pixel_coords = np.column_stack((y_coords, x_coords)) # Shape (N, 2)\n", "        centroids_arr = np.array(centroids)\n", "        if len(centroids_arr) == 0:\n", "            return flow_field\n", "        # Calculate distances for all spot pixels at once to all centroids\n", "        distances = cdist(pixel_coords, centroids_arr) # Shape (N, num_centroids)\n", "        nearest_idx = np.argmin(distances, axis=1) # Shape (N,)\n", "        # Calculate flow vectors (unit vectors pointing to nearest centroid)\n", "        nearest_centroids = centroids_arr[nearest_idx] # Shape (N, 2)\n", "        delta = nearest_centroids - pixel_coords # Shape (N, 2) - (dy, dx)\n", "        norms = np.linalg.norm(delta, axis=1, keepdims=True) # Shape (N, 1)\n", "        norms = np.where(norms == 0, 1, norms) # Avoid division by zero\n", "        unit_vectors = delta / norms # Shape (N, 2) - normalized flow vectors (dy, dx)\n", "        # Assign flow vectors back to the full flow field array\n", "        flow_field[0, y_coords, x_coords] = unit_vectors[:, 0] # dy component\n", "        flow_field[1, y_coords, x_coords] = unit_vectors[:, 1] # dx component\n", "        return flow_field\n", "\n", "    def __len__(self):\n", "        return len(self.image_paths)\n", "\n", "    def __getitem__(self, idx):\n", "        # --- 1. Load and Preprocess Image ---\n", "        image = tifffile.imread(self.image_paths[idx]).astype(np.float32)\n", "        mask = tifffile.imread(self.mask_paths[idx]).astype(np.int32)\n", "        # Robust percentile-based normalization\n", "        vmin, vmax = np.percentile(image, (1, 99))\n", "        image = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)\n", "        H, W = image.shape\n", "        # --- 2. <PERSON> Extraction ---\n", "        valid_ids = self._get_valid_spots(mask)\n", "        # Prefer patches containing spots\n", "        if len(valid_ids) > 0 and random.random() < 0.7:\n", "            spot_id = random.choice(valid_ids)\n", "            coords = np.argwhere(mask == spot_id)\n", "            # Calculate centroid of the selected spot\n", "            cy, cx = coords.mean(axis=0).astype(int) # cy is row (y), cx is col (x)\n", "            # --- CORRECTED Patch Extraction Coordinates ---\n", "            # Use cy (row) for y0 and cx (col) for x0\n", "            y0 = np.clip(cy - self.patch_size // 2, 0, H - self.patch_size) \n", "            x0 = np.clip(cx - self.patch_size // 2, 0, W - self.patch_size) \n", "        else:\n", "            # Random patch\n", "            y0 = random.randint(0, max(0, H - self.patch_size))\n", "            x0 = random.randint(0, max(0, W - self.patch_size))\n", "        # --- CORRECTED Patch Extraction (y first, then x) ---\n", "        patch_img = image[y0:y0+self.patch_size, x0:x0+self.patch_size]\n", "        patch_mask = mask[y0:y0+self.patch_size, x0:x0+self.patch_size]\n", "        # Padding if patch goes beyond image boundaries (should be rare with clipping)\n", "        if patch_img.shape[0] < self.patch_size or patch_img.shape[1] < self.patch_size:\n", "            ph = max(0, self.patch_size - patch_img.shape[0])\n", "            pw = max(0, self.patch_size - patch_img.shape[1])\n", "            patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')\n", "            patch_mask = np.pad(patch_mask, ((0, ph), (0, pw)), 'constant')\n", "        # --- 3. Process Targets ---\n", "        valid_ids = self._get_valid_spots(patch_mask)\n", "        if len(valid_ids) == 0:\n", "            # Return zero tensors if no spots are in the patch\n", "            return (torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32),\n", "                    torch.zeros((7, self.patch_size, self.patch_size), dtype=torch.float32))\n", "        centroids, spot_masks = self._extract_spot_info(patch_mask, valid_ids)\n", "        semantic = self._generate_semantic_mask(spot_masks, patch_mask.shape)\n", "        # Combine SDT, Skeleton, and Boundary for all spots in the patch\n", "        combined_sdt = np.zeros_like(semantic, dtype=np.float32)\n", "        combined_skeleton = np.zeros_like(semantic, dtype=np.float32)\n", "        combined_boundary = np.zeros_like(semantic, dtype=np.float32)\n", "        for spot_mask in spot_masks:\n", "            sdt, skeleton, boundary = self._generate_skeleton_aware_distance_transform(spot_mask)\n", "            # Use np.maximum to combine, taking the highest value at each pixel\n", "            combined_sdt = np.maximum(combined_sdt, sdt)\n", "            combined_skeleton = np.maximum(combined_skeleton, skeleton)\n", "            combined_boundary = np.maximum(combined_boundary, boundary)\n", "        centroid_map = self._generate_centroid_map(patch_mask.shape, centroids)\n", "        flow_field = self._generate_flow_field(semantic, centroids) # Shape: (2, H, W)\n", "        # --- 4. <PERSON><PERSON>s ---\n", "        # Order: [semantic, sdt, skeleton, centroid, flow_y, flow_x, boundary]\n", "        targets = np.stack([\n", "            semantic, \n", "            combined_sdt, \n", "            combined_skeleton, \n", "            centroid_map,\n", "            flow_field[0], # flow_y\n", "            flow_field[1], # flow_x\n", "            combined_boundary\n", "        ], axis=0)\n", "        # --- 5. Apply Augmentations ---\n", "        if self.transform is not None:\n", "            # Move channels to the end for Albumentations (H, W, C)\n", "            targets_hwc = np.moveaxis(targets, 0, -1)\n", "            augmented = self.transform(image=patch_img, mask=targets_hwc)\n", "            patch_img = augmented['image']\n", "            targets = np.moveaxis(augmented['mask'], -1, 0) # Move channels back to front (C, H, W)\n", "            # Post-augmentation cleanup to ensure value ranges are correct\n", "            targets[1] = np.clip(targets[1], 0, 1)  # SDT\n", "            targets[2] = np.clip(targets[2], 0, 1)  # Skeleton - preserve gradients!\n", "            targets[0] = (targets[0] > 0.5).astype(np.float32)  # Semantic - binary\n", "            targets[6] = (targets[6] > 0.5).astype(np.float32)  # Boundary - binary\n", "        # --- 6. Convert to <PERSON><PERSON> ---\n", "        img_tensor = torch.from_numpy(patch_img.astype(np.float32)).unsqueeze(0) # Add channel dim\n", "        targets_tensor = torch.from_numpy(targets.astype(np.float32))\n", "        return img_tensor, targets_tensor\n", "\n", "print(\"SkeletonAwareSpotDataset class is ready.\")"]}, {"cell_type": "code", "execution_count": 21, "id": "7b62556f", "metadata": {}, "outputs": [], "source": ["# # Cell: Corrected and Improved SkeletonAwareSpotDataset\n", "\n", "# import torch\n", "# import numpy as np\n", "# import tifffile\n", "# import random\n", "# import cv2\n", "# from torch.utils.data import Dataset\n", "# from scipy.ndimage import distance_transform_edt\n", "# from skimage.measure import regionprops\n", "# from skimage.morphology import skeletonize\n", "# from scipy.spatial.distance import cdist\n", "\n", "# class SkeletonAwareSpotDataset(Dataset):\n", "#     def __init__(self, image_paths, mask_paths, transform=None, patch_size=256):\n", "#         assert len(image_paths) == len(mask_paths)\n", "#         self.image_paths = image_paths\n", "#         self.mask_paths = mask_paths\n", "#         self.transform = transform\n", "#         self.patch_size = patch_size\n", "\n", "#     def _get_valid_spots(self, mask):\n", "#         \"\"\"Get list of valid spot IDs (excluding background 0).\"\"\"\n", "#         valid_ids = np.unique(mask)\n", "#         return valid_ids[valid_ids > 0]\n", "\n", "#     def _extract_spot_info(self, mask, valid_ids):\n", "#         \"\"\"Extract centroids and individual spot masks.\"\"\"\n", "#         centroids = []\n", "#         spot_masks = []\n", "#         for spot_id in valid_ids:\n", "#             spot_mask = (mask == spot_id).astype(np.uint8)\n", "#             props = regionprops(spot_mask)\n", "#             if props:\n", "#                 centroids.append(props[0].centroid)\n", "#                 spot_masks.append(spot_mask)\n", "#         return centroids, spot_masks\n", "\n", "#     def _generate_semantic_mask(self, spot_masks, shape):\n", "#         \"\"\"Combine individual spot masks into a single semantic mask.\"\"\"\n", "#         semantic = np.zeros(shape, dtype=np.float32)\n", "#         for mask in spot_masks:\n", "#             semantic = np.maximum(semantic, mask.astype(np.float32))\n", "#         return semantic\n", "\n", "#     def _generate_skeleton_aware_distance_transform(self, spot_mask):\n", "#         \"\"\"\n", "#         Generate SDT, Skeleton, and Boundary maps for a single spot mask.\n", "#         Corrected to calculate internal distance to skeleton properly.\n", "#         \"\"\"\n", "#         if not np.any(spot_mask):\n", "#             return (np.zeros_like(spot_mask, dtype=np.float32), \n", "#                     np.zeros_like(spot_mask, dtype=np.float32),\n", "#                     np.zeros_like(spot_mask, dtype=np.float32))\n", "\n", "#         spot_area = np.sum(spot_mask)\n", "#         H, W = spot_mask.shape\n", "\n", "#         # --- 1. Generate skeleton ---\n", "#         skeleton = np.zeros_like(spot_mask, dtype=np.float32)\n", "#         if spot_area <= 10:  # Small spots\n", "#             props = regionprops(spot_mask.astype(np.uint8))\n", "#             if props:\n", "#                 cy, cx = map(int, props[0].centroid)\n", "#                 # Create a small 3x3 region with gradient values\n", "#                 for dy in [-1, 0, 1]:\n", "#                     for dx in [-1, 0, 1]:\n", "#                         ny, nx = cy + dy, cx + dx\n", "#                         if 0 <= ny < H and 0 <= nx < W and spot_mask[ny, nx]:\n", "#                             skeleton[ny, nx] = 1.0 if (dy == 0 and dx == 0) else 0.6\n", "#         else:  # Larger spots\n", "#             try:\n", "#                 skel_temp = skeletonize(spot_mask > 0)\n", "#                 skeleton = skel_temp.astype(np.float32)\n", "#                 # Fallback if skeletonize produces an empty mask\n", "#                 if np.sum(skeleton) == 0:\n", "#                     props = regionprops(spot_mask.astype(np.uint8))\n", "#                     if props:\n", "#                         cy, cx = map(int, props[0].centroid)\n", "#                         if 0 <= cy < H and 0 <= cx < W:\n", "#                             skeleton[cy, cx] = 1.0\n", "#             except Exception:\n", "#                 # Final fallback to centroid\n", "#                 props = regionprops(spot_mask.astype(np.uint8))\n", "#                 if props:\n", "#                     cy, cx = map(int, props[0].centroid)\n", "#                     if 0 <= cy < H and 0 <= cx < W:\n", "#                         skeleton[cy, cx] = 1.0\n", "\n", "#         # --- 2. Generate boundary ---\n", "#         kernel = np.ones((3, 3), np.uint8)\n", "#         eroded = cv2.erode(spot_mask.astype(np.uint8), kernel, iterations=1)\n", "#         boundary = (spot_mask.astype(np.uint8) - eroded).astype(np.float32)\n", "\n", "#         # --- 3. Generate SDT (Corrected Logic) ---\n", "#         spot_region = spot_mask > 0\n", "\n", "#         # Distance to boundary (increases from boundary inward)\n", "#         dist_to_boundary = distance_transform_edt(spot_mask)\n", "        \n", "#         # --- CRITICAL FIX: Internal Distance to Skeleton ---\n", "#         skeleton_binary = skeleton > 0\n", "#         if np.any(spot_region) and np.any(skeleton_binary):\n", "#             # Get coordinates of spot pixels and skeleton pixels\n", "#             coords_spot = np.argwhere(spot_region)\n", "#             coords_skel = np.argwhere(skeleton_binary)\n", "            \n", "#             if len(coords_skel) > 0:\n", "#                 # Calculate distance from each spot pixel to the nearest skeleton pixel\n", "#                 dists = cdist(coords_spot, coords_skel, 'euclidean')\n", "#                 min_dists = np.min(dists, axis=1)\n", "                \n", "#                 # Create internal distance map (same shape as spot_mask)\n", "#                 dist_to_skeleton_internal = np.full((H, W), np.inf, dtype=np.float32)\n", "#                 for (y, x), d in zip(coords_spot, min_dists):\n", "#                     dist_to_skeleton_internal[y, x] = d\n", "                \n", "#                 # Normalize distance to skeleton (0 at skeleton, increasing outward within spot)\n", "#                 max_skel_dist = np.max(min_dists) if len(min_dists) > 0 else 1.0\n", "#                 skeleton_norm = dist_to_skeleton_internal / (max_skel_dist + 1e-6)\n", "#                 skeleton_norm[~spot_region] = 0 # Ensure outside spot is 0\n", "#             else:\n", "#                 skeleton_norm = np.zeros_like(spot_mask, dtype=np.float32)\n", "#         else:\n", "#             skeleton_norm = np.zeros_like(spot_mask, dtype=np.float32)\n", "#         # --- END CRITICAL FIX ---\n", "\n", "#         # Normalize distance to boundary\n", "#         if np.any(spot_region):\n", "#             max_boundary_dist = np.max(dist_to_boundary[spot_region]) + 1e-6\n", "#             boundary_norm = dist_to_boundary / max_boundary_dist\n", "#             boundary_norm[~spot_region] = 0 # Ensure outside spot is 0\n", "\n", "#             # SDT formula (aligning with paper intent)\n", "#             alpha = 1.5 if spot_area <= 15 else 1.2\n", "#             # SDT(x) = d_boundary(x) * (1 - d_skel(x))^α\n", "#             # Where d_skel(x) is normalized internal distance (0 at skel, 1 far away)\n", "#             sdt = boundary_norm * np.power(np.clip(1.0 - skeleton_norm, 0, 1), alpha)\n", "#             sdt = np.clip(sdt, 0, 1)\n", "#         else:\n", "#             sdt = np.zeros_like(spot_mask, dtype=np.float32)\n", "\n", "#         return sdt, skeleton, boundary\n", "\n", "#     def _generate_centroid_map(self, shape, centroids):\n", "#         \"\"\"Generate a centroid heatmap with peak values at spot centers.\"\"\"\n", "#         centroid_map = np.zeros(shape, dtype=np.float32)\n", "#         for cy, cx in centroids:\n", "#             cy, cx = int(round(cy)), int(round(cx))\n", "#             if 0 <= cy < shape[0] and 0 <= cx < shape[1]:\n", "#                 centroid_map[cy, cx] = 1.0\n", "#                 # Add a small gradient around the peak\n", "#                 for dy in [-1, 0, 1]:\n", "#                     for dx in [-1, 0, 1]:\n", "#                         ny, nx = cy + dy, cx + dx\n", "#                         if 0 <= ny < shape[0] and 0 <= nx < shape[1]:\n", "#                             if dy == 0 and dx == 0: continue\n", "#                             # Assign a lower value to neighbors\n", "#                             centroid_map[ny, nx] = max(centroid_map[ny, nx], 0.3) \n", "#         return centroid_map\n", "\n", "#     def _generate_flow_field(self, semantic_mask, centroids):\n", "#         \"\"\"\n", "#         Vectorized flow field generation for all pixels within semantic regions.\n", "#         Points from each pixel to its nearest centroid.\n", "#         \"\"\"\n", "#         h, w = semantic_mask.shape\n", "#         if len(centroids) == 0:\n", "#             return np.zeros((2, h, w), dtype=np.float32)\n", "\n", "#         flow_field = np.zeros((2, h, w), dtype=np.float32)\n", "#         # Get boolean mask for all spot pixels\n", "#         spot_mask_flat = semantic_mask.flatten()\n", "#         spot_indices = np.where(spot_mask_flat > 0)[0]\n", "\n", "#         if len(spot_indices) == 0:\n", "#             return flow_field\n", "\n", "#         # Get coordinates of all spot pixels\n", "#         y_coords, x_coords = np.unravel_index(spot_indices, (h, w))\n", "#         pixel_coords = np.column_stack((y_coords, x_coords)) # Shape (N, 2)\n", "\n", "#         centroids_arr = np.array(centroids)\n", "#         if len(centroids_arr) == 0:\n", "#             return flow_field\n", "\n", "#         # Calculate distances for all spot pixels at once to all centroids\n", "#         distances = cdist(pixel_coords, centroids_arr) # Shape (N, num_centroids)\n", "#         nearest_idx = np.argmin(distances, axis=1) # Shape (N,)\n", "\n", "#         # Calculate flow vectors (unit vectors pointing to nearest centroid)\n", "#         nearest_centroids = centroids_arr[nearest_idx] # Shape (N, 2)\n", "#         delta = nearest_centroids - pixel_coords # Shape (N, 2) - (dy, dx)\n", "#         norms = np.linalg.norm(delta, axis=1, keepdims=True) # Shape (N, 1)\n", "#         norms = np.where(norms == 0, 1, norms) # Avoid division by zero\n", "#         unit_vectors = delta / norms # Shape (N, 2) - normalized flow vectors (dy, dx)\n", "\n", "#         # Assign flow vectors back to the full flow field array\n", "#         flow_field[0, y_coords, x_coords] = unit_vectors[:, 0] # dy component\n", "#         flow_field[1, y_coords, x_coords] = unit_vectors[:, 1] # dx component\n", "\n", "#         return flow_field\n", "\n", "#     def __len__(self):\n", "#         return len(self.image_paths)\n", "\n", "#     def __getitem__(self, idx):\n", "#         # --- 1. Load and Preprocess Image ---\n", "#         image = tifffile.imread(self.image_paths[idx]).astype(np.float32)\n", "#         mask = tifffile.imread(self.mask_paths[idx]).astype(np.int32)\n", "        \n", "#         # Robust percentile-based normalization\n", "#         vmin, vmax = np.percentile(image, (1, 99))\n", "#         image = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)\n", "#         H, W = image.shape\n", "\n", "#         # --- 2. <PERSON> Extraction ---\n", "#         valid_ids = self._get_valid_spots(mask)\n", "#         # Prefer patches containing spots\n", "#         if len(valid_ids) > 0 and random.random() < 0.7:\n", "#             spot_id = random.choice(valid_ids)\n", "#             coords = np.argwhere(mask == spot_id)\n", "#             # Calculate centroid of the selected spot\n", "#             cy, cx = coords.mean(axis=0).astype(int) # cy is row (y), cx is col (x)\n", "#             # --- CORRECTED Patch Extraction Coordinates ---\n", "#             # Use cy (row) for y0 and cx (col) for x0\n", "#             y0 = np.clip(cy - self.patch_size // 2, 0, H - self.patch_size) \n", "#             x0 = np.clip(cx - self.patch_size // 2, 0, W - self.patch_size) \n", "#         else:\n", "#             # Random patch\n", "#             y0 = random.randint(0, max(0, H - self.patch_size))\n", "#             x0 = random.randint(0, max(0, W - self.patch_size))\n", "\n", "#         # --- CORRECTED Patch Extraction (y first, then x) ---\n", "#         patch_img = image[y0:y0+self.patch_size, x0:x0+self.patch_size]\n", "#         patch_mask = mask[y0:y0+self.patch_size, x0:x0+self.patch_size]\n", "\n", "#         # Padding if patch goes beyond image boundaries (should be rare with clipping)\n", "#         if patch_img.shape[0] < self.patch_size or patch_img.shape[1] < self.patch_size:\n", "#             ph = max(0, self.patch_size - patch_img.shape[0])\n", "#             pw = max(0, self.patch_size - patch_img.shape[1])\n", "#             patch_img = np.pad(patch_img, ((0, ph), (0, pw)), 'reflect')\n", "#             patch_mask = np.pad(patch_mask, ((0, ph), (0, pw)), 'constant')\n", "\n", "#         # --- 3. Process Targets ---\n", "#         valid_ids = self._get_valid_spots(patch_mask)\n", "#         if len(valid_ids) == 0:\n", "#             # Return zero tensors if no spots are in the patch\n", "#             return (torch.zeros((1, self.patch_size, self.patch_size), dtype=torch.float32),\n", "#                     torch.zeros((7, self.patch_size, self.patch_size), dtype=torch.float32))\n", "\n", "#         centroids, spot_masks = self._extract_spot_info(patch_mask, valid_ids)\n", "#         semantic = self._generate_semantic_mask(spot_masks, patch_mask.shape)\n", "\n", "#         # Combine SDT, Skeleton, and Boundary for all spots in the patch\n", "#         combined_sdt = np.zeros_like(semantic, dtype=np.float32)\n", "#         combined_skeleton = np.zeros_like(semantic, dtype=np.float32)\n", "#         combined_boundary = np.zeros_like(semantic, dtype=np.float32)\n", "\n", "#         for spot_mask in spot_masks:\n", "#             sdt, skeleton, boundary = self._generate_skeleton_aware_distance_transform(spot_mask)\n", "#             # Use np.maximum to combine, taking the highest value at each pixel\n", "#             combined_sdt = np.maximum(combined_sdt, sdt)\n", "#             combined_skeleton = np.maximum(combined_skeleton, skeleton)\n", "#             combined_boundary = np.maximum(combined_boundary, boundary)\n", "\n", "#         centroid_map = self._generate_centroid_map(patch_mask.shape, centroids)\n", "#         flow_field = self._generate_flow_field(semantic, centroids) # Shape: (2, H, W)\n", "\n", "#         # --- 4. <PERSON><PERSON>s ---\n", "#         # Order: [semantic, sdt, skeleton, centroid, flow_y, flow_x, boundary]\n", "#         targets = np.stack([\n", "#             semantic, \n", "#             combined_sdt, \n", "#             combined_skeleton, \n", "#             centroid_map,\n", "#             flow_field[0], # flow_y\n", "#             flow_field[1], # flow_x\n", "#             combined_boundary\n", "#         ], axis=0)\n", "\n", "#         # --- 5. Apply Augmentations ---\n", "#         if self.transform is not None:\n", "#             # Move channels to the end for Albumentations (H, W, C)\n", "#             targets_hwc = np.moveaxis(targets, 0, -1)\n", "#             augmented = self.transform(image=patch_img, mask=targets_hwc)\n", "#             patch_img = augmented['image']\n", "#             targets = np.moveaxis(augmented['mask'], -1, 0) # Move channels back to front (C, H, W)\n", "            \n", "#             # Post-augmentation cleanup to ensure value ranges are correct\n", "#             targets[1] = np.clip(targets[1], 0, 1)  # SDT\n", "#             targets[2] = np.clip(targets[2], 0, 1)  # Skeleton - preserve gradients!\n", "#             targets[0] = (targets[0] > 0.5).astype(np.float32)  # Semantic - binary\n", "#             targets[6] = (targets[6] > 0.5).astype(np.float32)  # Boundary - binary\n", "\n", "#         # --- 6. Convert to Ten<PERSON> ---\n", "#         img_tensor = torch.from_numpy(patch_img.astype(np.float32)).unsqueeze(0) # Add channel dim\n", "#         targets_tensor = torch.from_numpy(targets.astype(np.float32))\n", "        \n", "#         return img_tensor, targets_tensor\n", "\n", "# print(\"SkeletonAwareSpotDataset class is ready.\")"]}, {"cell_type": "code", "execution_count": 22, "id": "682952ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 395 spots in the mask (16-bit safe).\n", "Generated Gaussian centroid map with 395 peaks.\n", "✅ Full-image analysis saved to: full_image_analysis/full_image_analysis_grid.png\n", "✅ Total spots processed: 395\n", "✅ Centroids aligned: 395\n", "\n", "🎉 Full-image analysis complete.\n"]}], "source": ["# Cell: Full-Image Multi-Channel Visualization for Skeleton-Aware Spot Detection\n", "import os\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import tifffile\n", "from scipy.ndimage import distance_transform_edt\n", "from scipy.spatial.distance import cdist\n", "from skimage.measure import regionprops\n", "from skimage.morphology import skeletonize\n", "import cv2\n", "\n", "# --- 1. SDT Generation for a Single Spot ---\n", "def generate_skeleton_aware_distance_transform(spot_mask):\n", "    if not np.any(spot_mask):\n", "        return (np.zeros_like(spot_mask, dtype=np.float32),\n", "                np.zeros_like(spot_mask, dtype=np.float32),\n", "                np.zeros_like(spot_mask, dtype=np.float32))\n", "    \n", "    H, W = spot_mask.shape\n", "    spot_area = np.sum(spot_mask)\n", "    \n", "    # --- Generate Skeleton ---\n", "    skeleton = np.zeros_like(spot_mask, dtype=np.float32)\n", "    if spot_area <= 10:\n", "        props = regionprops(spot_mask.astype(np.uint16))\n", "        if props:\n", "            cy, cx = map(int, props[0].centroid)\n", "            Y, X = np.ogrid[:H, :W]\n", "            dist_from_center = np.sqrt((Y - cy)**2 + (X - cx)**2)\n", "            skeleton[spot_mask > 0] = np.clip(1.0 - dist_from_center[spot_mask > 0] / 2.0, 0, 1)\n", "    else:\n", "        try:\n", "            skel_temp = skeletonize(spot_mask > 0)\n", "            skeleton = skel_temp.astype(np.float32)\n", "            if np.sum(skeleton) == 0:\n", "                props = regionprops(spot_mask.astype(np.uint16))\n", "                if props:\n", "                    cy, cx = map(int, props[0].centroid)\n", "                    if 0 <= cy < H and 0 <= cx < W:\n", "                        skeleton[cy, cx] = 1.0\n", "        except Exception:\n", "            props = regionprops(spot_mask.astype(np.uint16))\n", "            if props:\n", "                cy, cx = map(int, props[0].centroid)\n", "                if 0 <= cy < H and 0 <= cx < W:\n", "                    skeleton[cy, cx] = 1.0\n", "\n", "    # --- Generate Boundary ---\n", "    kernel = np.ones((3, 3), np.uint8)\n", "    eroded = cv2.erode(spot_mask.astype(np.uint8), kernel, iterations=1)\n", "    boundary = (spot_mask.astype(np.uint8) - eroded).astype(np.float32)\n", "\n", "    # --- Generate SDT ---\n", "    spot_region = spot_mask > 0\n", "    dist_to_boundary = distance_transform_edt(spot_mask)\n", "\n", "    skeleton_binary = skeleton > 0\n", "    if np.any(spot_region) and np.any(skeleton_binary):\n", "        coords_spot = np.argwhere(spot_region)\n", "        coords_skel = np.argwhere(skeleton_binary)\n", "        if len(coords_skel) > 0:\n", "            dists = cdist(coords_spot, coords_skel, 'euclidean')\n", "            min_dists = np.min(dists, axis=1)\n", "            dist_to_skeleton_internal = np.full((H, W), np.inf, dtype=np.float32)\n", "            for (y, x), d in zip(coords_spot, min_dists):\n", "                dist_to_skeleton_internal[y, x] = d\n", "            max_skel_dist = np.max(min_dists) if len(min_dists) > 0 else 1.0\n", "            skeleton_norm = dist_to_skeleton_internal / (max_skel_dist + 1e-6)\n", "            skeleton_norm[~spot_region] = 0\n", "        else:\n", "            skeleton_norm = np.zeros_like(spot_mask, dtype=np.float32)\n", "    else:\n", "        skeleton_norm = np.zeros_like(spot_mask, dtype=np.float32)\n", "\n", "    if np.any(spot_region):\n", "        max_boundary_dist = np.max(dist_to_boundary[spot_region]) + 1e-6\n", "        boundary_norm = dist_to_boundary / max_boundary_dist\n", "        boundary_norm[~spot_region] = 0\n", "        alpha = 1.5 if spot_area <= 15 else 1.2\n", "        sdt = boundary_norm * np.power(np.clip(1.0 - skeleton_norm, 0, 1), alpha)\n", "        sdt = np.clip(sdt, 0, 1)\n", "    else:\n", "        sdt = np.zeros_like(spot_mask, dtype=np.float32)\n", "\n", "    return sdt, skeleton, boundary\n", "\n", "\n", "# --- 2. G<PERSON>sian Centroid Map for Multiple Spots ---\n", "def generate_centroid_map(shape, centroids):\n", "    centroid_map = np.zeros(shape, dtype=np.float32)\n", "    sigma = 1.0\n", "    for cy, cx in centroids:\n", "        cy_int, cx_int = int(round(cy)), int(round(cx))\n", "        y_min = max(0, cy_int - 6)\n", "        y_max = min(shape[0], cy_int + 7)\n", "        x_min = max(0, cx_int - 6)\n", "        x_max = min(shape[1], cx_int + 7)\n", "        if y_min >= y_max or x_min >= x_max:\n", "            continue\n", "        Y, X = np.ogrid[y_min:y_max, x_min:x_max]\n", "        g = np.exp(-((X - cx)**2 + (Y - cy)**2) / (2 * sigma**2))\n", "        existing = centroid_map[y_min:y_max, x_min:x_max]\n", "        centroid_map[y_min:y_max, x_min:x_max] = np.maximum(existing, g)\n", "    return centroid_map\n", "\n", "\n", "# --- 3. FULL-<PERSON><PERSON><PERSON> MULTI-CHANNEL VISUALIZATION ---\n", "def visualize_full_image_analysis(image_path, mask_path, output_dir=\"full_image_analysis\"):\n", "    \"\"\"\n", "    Create a 3x3 grid showing all model targets for the entire image.\n", "    No individual spot analysis — just full-image overviews.\n", "    \"\"\"\n", "    os.makedirs(output_dir, exist_ok=True)\n", "\n", "    # --- 1. Load 16-bit Image and Mask ---\n", "    image = tifffile.imread(image_path).astype(np.float32)\n", "    mask = tifffile.imread(mask_path).astype(np.uint16)  # Ensure 16-bit\n", "    vmin, vmax = np.percentile(image, (1, 99))\n", "    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1)\n", "\n", "    # --- 2. Extract All Valid Spots (16-bit safe) ---\n", "    valid_ids = np.unique(mask)\n", "    valid_ids = valid_ids[valid_ids > 0]  # Remove background\n", "    print(f\"Found {len(valid_ids)} spots in the mask (16-bit safe).\")\n", "\n", "    # Initialize accumulators\n", "    H, W = mask.shape\n", "    combined_semantic = np.zeros((H, W), dtype=np.float32)\n", "    combined_skeleton = np.zeros((H, W), dtype=np.float32)\n", "    combined_boundary = np.zeros((H, W), dtype=np.float32)\n", "    combined_sdt = np.zeros((H, W), dtype=np.float32)\n", "    all_centroids = []\n", "\n", "    # --- 3. <PERSON> All Spots ---\n", "    for spot_id in valid_ids:\n", "        spot_mask = (mask == spot_id).astype(np.uint16)\n", "        if np.sum(spot_mask) == 0:\n", "            continue\n", "\n", "        # Extract centroid\n", "        props = regionprops(spot_mask)\n", "        if props:\n", "            cy, cx = props[0].centroid\n", "            all_centroids.append((cy, cx))\n", "\n", "        # Generate maps\n", "        sdt, skeleton, boundary = generate_skeleton_aware_distance_transform(spot_mask)\n", "        combined_semantic = np.maximum(combined_semantic, spot_mask.astype(np.float32))\n", "        combined_skeleton = np.maximum(combined_skeleton, skeleton)\n", "        combined_boundary = np.maximum(combined_boundary, boundary)\n", "        combined_sdt = np.maximum(combined_sdt, sdt)\n", "\n", "    # --- 4. Generate Gaussian Centroid Map ---\n", "    centroid_map = generate_centroid_map((H, W), all_centroids)\n", "    print(f\"Generated Gaussian centroid map with {len(all_centroids)} peaks.\")\n", "\n", "    # --- 5. Instance Segmentation (Optional) ---\n", "    from skimage.segmentation import watershed\n", "    from scipy.ndimage import maximum_filter\n", "    peaks = (centroid_map > 0.3) & (maximum_filter(centroid_map, size=3) == centroid_map)\n", "    seeds = np.zeros((H, W), dtype=np.int32)\n", "    if len(all_centroids) > 0:\n", "        for idx, (y, x) in enumerate(all_centroids, 1):\n", "            if 0 <= y < H and 0 <= x < W:\n", "                seeds[int(y), int(x)] = idx\n", "    instance_labels = watershed(-combined_sdt, seeds, mask=(combined_semantic > 0.5))\n", "\n", "    # --- 6. <PERSON><PERSON> 3x3 Visualization ---\n", "    fig, axes = plt.subplots(3, 3, figsize=(20, 18))\n", "    fig.suptitle(\"Full-Image Multi-Channel Analysis\", fontsize=18)\n", "\n", "    # 1. Input Image\n", "    axes[0, 0].imshow(image_norm, cmap='gray')\n", "    axes[0, 0].set_title(\"Input Image\")\n", "    axes[0, 0].axis('off')\n", "\n", "    # 2. <PERSON><PERSON><PERSON> (All Spots)\n", "    axes[0, 1].imshow(combined_semantic, cmap='viridis')\n", "    axes[0, 1].set_title(\"Semantic Mask (All Spots)\")\n", "    axes[0, 1].axis('off')\n", "\n", "    # 3. <PERSON><PERSON><PERSON> (All Spots)\n", "    axes[0, 2].imshow(combined_skeleton, cmap='bone')\n", "    axes[0, 2].set_title(\"Skeleton (All Spots)\")\n", "    axes[0, 2].axis('off')\n", "\n", "    # 4. <PERSON><PERSON><PERSON> (All Spots)\n", "    axes[1, 0].imshow(combined_boundary, cmap='gray')\n", "    axes[1, 0].set_title(\"Boundary (All Spots)\")\n", "    axes[1, 0].axis('off')\n", "\n", "    # 5. SDT (All Spots)\n", "    im5 = axes[1, 1].imshow(combined_sdt, cmap='magma')\n", "    axes[1, 1].set_title(\"SDT (All Spots)\")\n", "    plt.colorbar(im5, ax=axes[1, 1], shrink=0.8)\n", "\n", "    # 6. Gaussian Centroid Map (All Spots)\n", "    im6 = axes[1, 2].imshow(centroid_map, cmap='coolwarm')\n", "    axes[1, 2].set_title(\"Gaussian Centroid Map (All Spots)\")\n", "    plt.colorbar(im6, ax=axes[1, 2], shrink=0.8)\n", "\n", "    # 7. Spot + Skeleton Overlay\n", "    axes[2, 0].imshow(combined_semantic, cmap='gray')\n", "    axes[2, 0].imshow(combined_skeleton, cmap='Reds', alpha=0.6)\n", "    axes[2, 0].set_title(\"Spot + Skeleton Overlay\")\n", "    axes[2, 0].axis('off')\n", "\n", "    # 8. Spot + Centroid Overlay\n", "    axes[2, 1].imshow(combined_semantic, cmap='gray')\n", "    axes[2, 1].imshow(centroid_map, cmap='coolwarm', alpha=0.6)\n", "    y_coords, x_coords = zip(*all_centroids) if all_centroids else ([], [])\n", "    axes[2, 1].scatter(x_coords, y_coords, c='red', s=30, marker='x', label='Centroid')\n", "    axes[2, 1].legend()\n", "    axes[2, 1].set_title(\"Spot + Centroid Overlay\")\n", "    axes[2, 1].axis('off')\n", "\n", "    # 9. Instance Labels\n", "    axes[2, 2].imshow(instance_labels, cmap='nipy_spectral')\n", "    axes[2, 2].set_title(\"Instance Labels\")\n", "    axes[2, 2].axis('off')\n", "\n", "    plt.tight_layout()\n", "    plot_filename = os.path.join(output_dir, \"full_image_analysis_grid.png\")\n", "    plt.savefig(plot_filename, dpi=150, bbox_inches='tight')\n", "    plt.close()\n", "\n", "    print(f\"✅ Full-image analysis saved to: {plot_filename}\")\n", "    print(f\"✅ Total spots processed: {len(valid_ids)}\")\n", "    print(f\"✅ Centroids aligned: {len(all_centroids)}\")\n", "\n", "\n", "# --- 4. Example Usage ---\n", "if __name__ == \"__main__\":\n", "    image_path = \"/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/synthetic_000003.tif\"\n", "    mask_path = \"/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/synthetic_000003.tif\"\n", "    \n", "    visualize_full_image_analysis(image_path, mask_path)\n", "    print(\"\\n🎉 Full-image analysis complete.\")"]}, {"cell_type": "code", "execution_count": 23, "id": "ee751769", "metadata": {}, "outputs": [], "source": ["# Cell: Updated Data Loader Creation (Fix Pin Memory)\n", "def create_skeleton_aware_data_loaders_fixed(image_paths, mask_paths, batch_size=12, \n", "                                             patch_size=256, num_workers=4):\n", "    \"\"\"\n", "    Fixed data loader creation without pin_memory to avoid CUDA OOM errors.\n", "    \"\"\"\n", "    # Validate dataset\n", "    assert len(image_paths) == len(mask_paths), \"Image and mask counts must match\"\n", "    assert all(os.path.exists(p) for p in image_paths), \"Some image paths are invalid\"\n", "    assert all(os.path.exists(p) for p in mask_paths), \"Some mask paths are invalid\"\n", "    \n", "    # Split data\n", "    train_imgs, val_imgs, train_masks, val_masks = train_test_split(\n", "        image_paths, mask_paths, test_size=0.2, random_state=42, shuffle=True\n", "    )\n", "    \n", "    print(f\"Dataset split: {len(train_imgs)} training, {len(val_imgs)} validation samples\")\n", "    \n", "    # Datasets\n", "    train_dataset = SkeletonAwareSpotDataset(\n", "        train_imgs, train_masks,\n", "        transform=get_optimized_transforms(),\n", "        patch_size=patch_size\n", "    )\n", "    val_dataset = SkeletonAwareSpotDataset(\n", "        val_imgs, val_masks,\n", "        transform=None,\n", "        patch_size=patch_size\n", "    )\n", "    \n", "    # Worker initialization function for deterministic behavior\n", "    def worker_init_fn(worker_id):\n", "        worker_seed = torch.initial_seed() % 2**32\n", "        np.random.seed(worker_seed)\n", "        random.seed(worker_seed)\n", "    \n", "    # Calculate optimal prefetch factor\n", "    prefetch_factor = max(2, batch_size // 4)\n", "    \n", "    # --- FIX: Disable pin_memory to prevent CUDA OOM ---\n", "    use_pin_memory = False # Set to False to avoid pin memory errors\n", "    \n", "    # DataLoaders with optimized settings\n", "    train_loader = DataLoader(\n", "        train_dataset,\n", "        batch_size=batch_size,\n", "        shuffle=True,\n", "        num_workers=num_workers,\n", "        pin_memory=use_pin_memory, # Use the fixed value\n", "        drop_last=True,\n", "        prefetch_factor=prefetch_factor,\n", "        persistent_workers=True if num_workers > 0 else False,\n", "        worker_init_fn=worker_init_fn\n", "    )\n", "    \n", "    val_loader = DataLoader(\n", "        val_dataset,\n", "        batch_size=batch_size,\n", "        shuffle=False,\n", "        num_workers=max(0, num_workers // 2),\n", "        pin_memory=use_pin_memory, # Use the fixed value\n", "        drop_last=False,\n", "        prefetch_factor=prefetch_factor,\n", "        persistent_workers=True if num_workers > 0 else False\n", "    )\n", "    \n", "    # Verify first batch loads correctly (without pin_memory)\n", "    try:\n", "        images, targets = next(iter(train_loader))\n", "        print(f\"Data loader verified: Batch shape - images {images.shape}, targets {targets.shape}\")\n", "        print(f\"Data types: images {images.dtype}, targets {targets.dtype}\")\n", "    except Exception as e:\n", "        print(f\"WARNING: Data loader verification failed: {e}\")\n", "    \n", "    return train_loader, val_loader\n", "\n", "# Update your main training script to use this function:\n", "# train_loader, val_loader = create_skeleton_aware_data_loaders_fixed(\n", "#     image_paths, mask_paths, \n", "#     batch_size=12, \n", "#     patch_size=256, \n", "#     num_workers=4\n", "# )"]}, {"cell_type": "code", "execution_count": 24, "id": "eccd7f1e", "metadata": {}, "outputs": [], "source": ["# Cell 3: Model - Corrected and Improved\n", "class EfficientBlock(nn.Module):\n", "    def __init__(self, in_ch, out_ch, stride=1, expand_ratio=4, dropout_prob=0.1):\n", "        super().__init__()\n", "        hidden_ch = in_ch * expand_ratio\n", "        self.stride = stride\n", "        # Pointwise expansion\n", "        if expand_ratio != 1:\n", "            self.expand = nn.Sequential(\n", "                nn.Conv2d(in_ch, hidden_ch, 1, bias=False),\n", "                nn.BatchNorm2d(hidden_ch),\n", "                nn.SiLU(inplace=True)\n", "            )\n", "        else:\n", "            self.expand = nn.Identity()\n", "        # Depthwise convolution\n", "        self.depthwise = nn.Sequential(\n", "            nn.Conv2d(hidden_ch, hidden_ch, 3, stride, 1, groups=hidden_ch, bias=False),\n", "            nn.BatchNorm2d(hidden_ch),\n", "            nn.SiLU(inplace=True)\n", "        )\n", "        # Squeeze-and-excitation\n", "        self.se = nn.Sequential(\n", "            nn.AdaptiveAvgPool2d(1),\n", "            nn.Conv2d(hidden_ch, max(1, hidden_ch//4), 1),\n", "            nn.SiLU(inplace=True),\n", "            nn.Conv2d(max(1, hidden_ch//4), hidden_ch, 1),\n", "            nn.<PERSON><PERSON><PERSON><PERSON>()\n", "        )\n", "        # Pointwise projection\n", "        self.project = nn.Sequential(\n", "            nn.Conv2d(hidden_ch, out_ch, 1, bias=False),\n", "            nn.BatchNorm2d(out_ch)\n", "        )\n", "        self.skip = (stride == 1 and in_ch == out_ch)\n", "        self.dropout = nn.Dropout2d(dropout_prob) if stride != 1 and in_ch != out_ch else nn.Identity()\n", "\n", "    def forward(self, x):\n", "        residual = x\n", "        x = self.expand(x)\n", "        x = self.depthwise(x)\n", "        x = x * self.se(x)\n", "        x = self.project(x)\n", "        x = self.dropout(x)\n", "        if self.skip:\n", "            scale = 1.0 / (2.0 ** 0.5)\n", "            return scale * (x + residual)\n", "        return x\n", "\n", "class SimpleSpatialAttention(nn.Module):\n", "    def __init__(self, in_channels):\n", "        super().__init__()\n", "        self.conv = nn.Conv2d(in_channels, 1, kernel_size=7, padding=3, bias=False)\n", "        self.sigmoid = nn.Sigmoid()\n", "        self.bn = nn.BatchNorm2d(1)\n", "\n", "    def forward(self, x):\n", "        attn = self.conv(x)\n", "        attn = self.bn(attn)\n", "        attn = self.sigmoid(attn)\n", "        return x * attn\n", "\n", "class SkeletonAwareSpotDetector(nn.Module):\n", "    def __init__(self, in_ch=1, base_ch=48, dropout_prob=0.1):\n", "        super().__init__()\n", "        # Stem\n", "        self.stem_conv1 = nn.Sequential(\n", "            nn.Conv2d(in_ch, base_ch, 3, padding=1, bias=False),\n", "            nn.BatchNorm2d(base_ch),\n", "            nn.SiLU(inplace=True)\n", "        )\n", "        self.stem_conv2 = nn.Sequential(\n", "            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),\n", "            nn.BatchNorm2d(base_ch),\n", "            nn.SiLU(inplace=True),\n", "            nn.Dropout2d(dropout_prob)\n", "        )\n", "        # Encoder\n", "        self.enc1 = nn.Sequential(\n", "            EfficientBlock(base_ch, base_ch*2, stride=2, dropout_prob=dropout_prob),\n", "            EfficientBlock(base_ch*2, base_ch*2, dropout_prob=dropout_prob)\n", "        )\n", "        self.enc2 = nn.Sequential(\n", "            EfficientBlock(base_ch*2, base_ch*4, stride=2, dropout_prob=dropout_prob),\n", "            EfficientBlock(base_ch*4, base_ch*4, dropout_prob=dropout_prob)\n", "        )\n", "        # FPN\n", "        self.fpn_lateral_stem = nn.Conv2d(base_ch, base_ch, 1)\n", "        self.fpn_conv2 = nn.Sequential(\n", "            nn.Conv2d(base_ch*4, base_ch*2, 1),\n", "            nn.<PERSON>chNorm2d(base_ch*2),\n", "            nn.SiLU(inplace=True)\n", "        )\n", "        self.fpn_conv1 = nn.Sequential(\n", "            nn.Conv2d(base_ch*2, base_ch, 1),\n", "            nn.BatchNorm2d(base_ch),\n", "            nn.SiLU(inplace=True)\n", "        )\n", "        self.fusion_conv = nn.Sequential(\n", "            nn.Conv2d(base_ch*2, base_ch, 1),\n", "            nn.BatchNorm2d(base_ch),\n", "            nn.SiLU(inplace=True)\n", "        )\n", "        self.skip_conv1 = nn.Conv2d(base_ch, base_ch, 1)\n", "        self.skip_conv2 = nn.Conv2d(base_ch, base_ch, 1)\n", "        # Decoder\n", "        self.decoder = nn.Sequential(\n", "            EfficientBlock(base_ch*3, base_ch*2, dropout_prob=dropout_prob),\n", "            EfficientBlock(base_ch*2, base_ch, dropout_prob=dropout_prob),\n", "            EfficientBlock(base_ch, base_ch, dropout_prob=dropout_prob),\n", "            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),\n", "            nn.BatchNorm2d(base_ch),\n", "            nn.SiLU(inplace=True),\n", "            nn.Dropout2d(dropout_prob)\n", "        )\n", "        # Attention\n", "        self.spatial_attn = SimpleSpatialAttention(base_ch)\n", "        # Shared head\n", "        self.shared_head = nn.Sequential(\n", "            nn.Conv2d(base_ch, base_ch, 3, padding=1, groups=base_ch, bias=False),\n", "            nn.BatchNorm2d(base_ch),\n", "            nn.SiLU(inplace=True),\n", "            nn.Conv2d(base_ch, base_ch, 1, bias=False),\n", "            nn.BatchNorm2d(base_ch),\n", "            nn.SiLU(inplace=True)\n", "        )\n", "        # Output heads\n", "        self.sdt_head = nn.Sequential(\n", "            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),\n", "            nn.BatchNorm2d(base_ch),\n", "            nn.SiLU(inplace=True),\n", "            nn.Conv2d(base_ch, 11, 1)  # 10 bins + 1 background\n", "        )\n", "        self.skeleton_head = nn.Sequential(\n", "            nn.Conv2d(base_ch, base_ch, 3, padding=1, bias=False),\n", "            nn.BatchNorm2d(base_ch),\n", "            nn.SiLU(inplace=True),\n", "            nn.Conv2d(base_ch, 1, 1)\n", "        )\n", "        self.semantic_head = nn.Sequential(nn.Conv2d(base_ch, 1, 1), nn.<PERSON><PERSON><PERSON><PERSON>())\n", "        self.centroid_head = nn.Sequential(nn.Conv2d(base_ch, 1, 1), nn.<PERSON><PERSON><PERSON><PERSON>())\n", "        self.flow_head = nn.Conv2d(base_ch, 2, 1)  # 2-channel flow field\n", "        self._init_weights()\n", "\n", "    def _init_weights(self):\n", "        for m in self.modules():\n", "            if isinstance(m, nn.Conv2d):\n", "                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')\n", "                if m.bias is not None: nn.init.zeros_(m.bias)\n", "            elif isinstance(m, nn.BatchNorm2d):\n", "                nn.init.ones_(m.weight)\n", "                nn.init.zeros_(m.bias)\n", "            elif isinstance(m, nn.Linear):\n", "                nn.init.trunc_normal_(m.weight, std=0.02)\n", "                if m.bias is not None: nn.init.zeros_(m.bias)\n", "            elif isinstance(m, nn.Conv2d) and m.out_channels == 11:\n", "                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='linear')\n", "                if m.bias is not None: nn.init.constant_(m.bias, 0)\n", "            elif isinstance(m, nn.Conv2d) and m.out_channels == 2:\n", "                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu', a=0.1)\n", "                if m.bias is not None: nn.init.zeros_(m.bias)\n", "\n", "    def forward(self, x):\n", "        B, C, H, W = x.shape\n", "        # Encoder\n", "        x0 = self.stem_conv2(self.stem_conv1(x))\n", "        x1 = self.enc1(x0)\n", "        x2 = self.enc2(x1)\n", "        # FPN\n", "        p2 = self.fpn_conv2(x2)\n", "        p2_up = F.interpolate(p2, size=(H//2, W//2), mode='bilinear', align_corners=False)\n", "        p1_in = x1 + p2_up\n", "        p1 = self.fpn_conv1(p1_in)\n", "        p1_up = F.interpolate(p1, size=(H, W), mode='bilinear', align_corners=False)\n", "        lateral_x0 = self.fpn_lateral_stem(x0)\n", "        fused_features = torch.cat([lateral_x0, p1_up], dim=1)\n", "        p1_up_final = self.fusion_conv(fused_features)\n", "        # Attention & Fusion\n", "        attention_map = torch.sigmoid(torch.mean(p1_up_final, dim=1, keepdim=True))\n", "        attended = x0 * (0.5 + 0.5 * attention_map)\n", "        x0_scaled = self.skip_conv1(x0)\n", "        p1_up_scaled = self.skip_conv2(p1_up_final)\n", "        fused = torch.cat([x0_scaled, p1_up_scaled, attended], dim=1)\n", "        # Decoder\n", "        features = self.decoder(fused)\n", "        features = self.spatial_attn(features)\n", "        shared_feat = self.shared_head(features)\n", "        # Outputs\n", "        sdt_out = self.sdt_head(shared_feat)\n", "        skeleton_out = self.skeleton_head(shared_feat)\n", "        flow_out = self.flow_head(shared_feat)\n", "        # Flow magnitude constraint\n", "        flow_magnitude = torch.sqrt(torch.sum(flow_out ** 2, dim=1, keepdim=True) + 1e-6)\n", "        scale = torch.min(torch.ones_like(flow_magnitude), 1.5 / (flow_magnitude + 1e-6))\n", "        constrained_flow_out = flow_out * scale\n", "        \n", "        return {\n", "            'sem_out': self.semantic_head(shared_feat),\n", "            'sdt_out': sdt_out,\n", "            'skeleton_out': skeleton_out,\n", "            'hm_out': self.centroid_head(shared_feat),\n", "            'flow_out': constrained_flow_out\n", "        }\n"]}, {"cell_type": "code", "execution_count": 25, "id": "9ba91a5e", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "\n", "class QuantizedSDTLoss(nn.Module):\n", "    def __init__(self, num_bins=10, background_value=0.0):\n", "        super().__init__()\n", "        self.num_bins = num_bins\n", "        self.background_value = background_value\n", "        self.eps = 1e-6\n", "\n", "    def forward(self, pred, target, mask=None):\n", "        target_sq = target.squeeze(1)\n", "        if mask is not None:\n", "            mask_sq = mask.squeeze(1)\n", "            target_classes = torch.zeros_like(target_sq, dtype=torch.long)\n", "            for i in range(self.num_bins):\n", "                bin_min = i / self.num_bins\n", "                bin_max = (i + 1) / self.num_bins\n", "                bin_mask = (target_sq > bin_min) & (target_sq <= bin_max) & (mask_sq > 0)\n", "                target_classes[bin_mask] = i + 1\n", "        else:\n", "            target_classes = torch.clamp((target_sq * self.num_bins).long(), 0, self.num_bins)\n", "            \n", "        base_loss = F.cross_entropy(pred, target_classes, reduction='none')\n", "        boundary_weight = 1.0 + 0.2 * (1.0 - target_sq)  # Emphasize boundary\n", "        weighted_loss = base_loss * boundary_weight\n", "\n", "        if mask is not None:\n", "            weighted_loss = weighted_loss * mask.squeeze(1)\n", "            return weighted_loss.sum() / (mask.sum() + self.eps)\n", "        return weighted_loss.mean()\n", "\n", "\n", "class SkeletonAwareLoss(nn.Module):\n", "    def __init__(self):\n", "        super().__init__()\n", "        # Rebalanced weights based on improved training stability\n", "        self.w_sem = 1.0\n", "        self.w_sdt = 1.0     # Stable now with better SDT target\n", "        self.w_skel = 1.5    # Reduced slightly — skeleton now easier to learn\n", "        self.w_cent = 1.2    # Increased — Gaussian targets make centroid easier to learn\n", "        self.w_flow = 2.0    # Keep high — flow still challenging\n", "\n", "        self.quantized_sdt_loss = QuantizedSDTLoss(num_bins=10, background_value=0.0)\n", "        \n", "        # Curriculum learning\n", "        self.epoch = 0\n", "\n", "    def set_epoch(self, epoch):\n", "        \"\"\"Call at the start of each epoch for curriculum learning\"\"\"\n", "        self.epoch = epoch\n", "\n", "    def focal_loss(self, pred, target, alpha=0.25, gamma=2.0, label_smoothing=0.05):\n", "        target_smooth = target * (1 - label_smoothing) + 0.5 * label_smoothing\n", "        bce = F.binary_cross_entropy_with_logits(pred, target_smooth, reduction='none')\n", "        pt = torch.exp(-bce)\n", "        focal_weight = alpha * (1 - pt) ** gamma\n", "        return (focal_weight * bce).mean()\n", "\n", "    def dice_loss(self, pred, target, smooth=1e-6):\n", "        pred_sig = torch.sigmoid(pred)\n", "        intersection = (pred_sig * target).sum(dim=(2, 3))\n", "        union = pred_sig.sum(dim=(2, 3)) + target.sum(dim=(2, 3))\n", "        dice = (2 * intersection + smooth) / (union + smooth)\n", "        return 1 - dice.mean()\n", "\n", "    def gaussian_smoothness_loss(self, pred_map, sigma=1.0, kernel_size=5):\n", "        \"\"\"\n", "        Encourage smooth, Gaussian-like peaks in the centroid map.\n", "        Applied to the sigmoided output to match the target structure.\n", "        \"\"\"\n", "        pred_sig = torch.sigmoid(pred_map)\n", "        x = torch.arange(kernel_size) - kernel_size // 2\n", "        y = torch.arange(kernel_size) - kernel_size // 2\n", "        xx, yy = torch.meshgrid(x, y, indexing='ij')\n", "        kernel = torch.exp(-(xx**2 + yy**2) / (2 * sigma**2))\n", "        kernel = kernel / kernel.sum()\n", "        kernel = kernel.view(1, 1, kernel_size, kernel_size).to(pred_map.device)\n", "\n", "        smoothed = F.conv2d(pred_sig, kernel, padding=kernel_size//2)\n", "        smoothness_loss = F.l1_loss(pred_sig, smoothed, reduction='mean')\n", "        return smoothness_loss\n", "\n", "    def skeleton_loss(self, pred, target):\n", "        \"\"\"\n", "        Updated skeleton loss for improved small-spot gradients.\n", "        Uses smooth L1 + BCE, but now handles continuous skeleton values.\n", "        \"\"\"\n", "        pred_sig = torch.sigmoid(pred)\n", "        # Smooth L1 on continuous values (handles gradient-based skeletons)\n", "        smooth_l1 = F.smooth_l1_loss(pred_sig, target, reduction='mean')\n", "        # BCE on binarized target (skeleton > 0.5)\n", "        bce_loss = F.binary_cross_entropy_with_logits(pred, (target > 0.5).float(), reduction='mean')\n", "        return 0.7 * smooth_l1 + 0.3 * bce_loss\n", "\n", "    def flow_loss(self, pred_flow, gt_flow, mask):\n", "        \"\"\"Enhanced flow loss with normalization and masking.\"\"\"\n", "        if mask.sum() < 1:\n", "            return torch.tensor(0.0, device=pred_flow.device)\n", "\n", "        pred_norm = F.normalize(pred_flow, p=2, dim=1, eps=1e-6)\n", "        gt_norm = F.normalize(gt_flow, p=2, dim=1, eps=1e-6)\n", "        cosine_sim = (pred_norm * gt_norm).sum(dim=1, keepdim=True)\n", "        cosine_loss = 1 - cosine_sim\n", "\n", "        magnitude_loss = F.smooth_l1_loss(pred_flow, gt_flow, reduction='none')\n", "        combined_loss = 0.6 * cosine_loss + 0.4 * magnitude_loss\n", "        masked_loss = combined_loss * mask\n", "        return masked_loss.sum() / mask.sum()\n", "\n", "    def forward(self, outputs, targets):\n", "        # Targets: [sem, sdt, skel, cent, flow_y, flow_x, boundary]\n", "        gt_sem = targets[:, 0:1]\n", "        gt_sdt = targets[:, 1:2]\n", "        gt_skel = targets[:, 2:3]\n", "        gt_cent = targets[:, 3:4]      # Now a smooth Gaussian map\n", "        gt_flow_full = targets[:, 4:6]\n", "\n", "        losses = {}\n", "        mask = (gt_sem > 0.5).float()  # Semantic mask for spatial weighting\n", "\n", "        # --- <PERSON><PERSON><PERSON> Loss ---\n", "        sem_focal = self.focal_loss(outputs['sem_out'], gt_sem)\n", "        sem_dice = self.dice_loss(outputs['sem_out'], gt_sem)\n", "        losses['semantic'] = sem_focal + sem_dice\n", "\n", "        # --- SDT Loss (with curriculum) ---\n", "        sdt_weight = min(1.0, self.epoch / 20.0)  # Start earlier, converge faster\n", "        losses['sdt'] = self.quantized_sdt_loss(outputs['sdt_out'], gt_sdt, mask=mask) * sdt_weight\n", "\n", "        # --- Skeleton Loss ---\n", "        losses['skeleton'] = self.skeleton_loss(outputs['skeleton_out'], gt_skel)\n", "\n", "        # --- Centroid Loss (with smoothness) ---\n", "        cent_focal = self.focal_loss(outputs['hm_out'], gt_cent)\n", "        cent_dice = self.dice_loss(outputs['hm_out'], gt_cent)\n", "        smooth_weight = min(0.3, max(0.05, (self.epoch - 10) / 50.0))\n", "        cent_smooth = self.gaussian_smoothness_loss(outputs['hm_out']) * smooth_weight\n", "        losses['centroid'] = cent_focal + cent_dice + cent_smooth\n", "\n", "        # --- Flow Loss (with curriculum) ---\n", "        flow_weight = min(1.0, max(0.0, (self.epoch - 5) / 15.0))  # Start flow earlier\n", "        losses['flow'] = self.flow_loss(outputs['flow_out'], gt_flow_full, mask) * flow_weight\n", "\n", "        # --- Total Loss ---\n", "        total = (\n", "            self.w_sem * losses['semantic'] +\n", "            self.w_sdt * losses['sdt'] +\n", "            self.w_skel * losses['skeleton'] +\n", "            self.w_cent * losses['centroid'] +\n", "            self.w_flow * losses['flow']\n", "        )\n", "\n", "        losses['total'] = total\n", "        return total, losses"]}, {"cell_type": "code", "execution_count": 26, "id": "eef8eea5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["inspect_skeleton_aware_model function updated with fixes.\n"]}], "source": ["# Cell 8: Updated Inspection Function (Corrected)\n", "def inspect_skeleton_aware_model(model, data_loader, device, num_batches=3, save_dir=\"./model_inspection_results\"):\n", "    \"\"\"\n", "    Comprehensive inspection of the SkeletonAwareSpotDetector model outputs.\n", "    Updated to align with Gaussian centroid maps and improved small-spot handling.\n", "    \"\"\"\n", "    import os\n", "    import matplotlib.pyplot as plt\n", "    import numpy as np\n", "    from scipy.ndimage import maximum_filter, gaussian_filter\n", "    from skimage.segmentation import watershed\n", "    from skimage.measure import label\n", "    import torch\n", "    import torch.nn.functional as F\n", "    try:\n", "        from torch.amp import autocast   # PyTorch 2.0+\n", "    except ImportError:\n", "        from torch.cuda.amp import autocast   # PyTorch < 2.0\n", "\n", "    model.eval()\n", "    os.makedirs(save_dir, exist_ok=True)\n", "    inspection_results = {\n", "        'output_shapes': {},\n", "        'value_ranges': {},\n", "        'sdt_properties': [],\n", "        'flow_validity': [],\n", "        'centroid_accuracy': [],\n", "        'centroid_smoothness': [],\n", "        'instance_segmentation': [],\n", "        'passed_checks': 0,\n", "        'total_checks': 0\n", "    }\n", "\n", "    def check(condition, description):\n", "        \"\"\"Helper to track inspection results\"\"\"\n", "        inspection_results['total_checks'] += 1\n", "        if condition:\n", "            inspection_results['passed_checks'] += 1\n", "            status = \"✅ PASS\"\n", "        else:\n", "            status = \"❌ FAIL\"\n", "        print(f\"  {status}: {description}\")\n", "        return condition\n", "\n", "    with torch.no_grad():\n", "        for batch_idx, (images, targets) in enumerate(data_loader):\n", "            if batch_idx >= num_batches:\n", "                break\n", "            images = images.float().to(device)\n", "            targets = targets.float().to(device)\n", "            with autocast('cuda'):\n", "                outputs = model(images)\n", "            batch_size = images.shape[0]\n", "\n", "            # 1. Check output shapes\n", "            if batch_idx == 0:\n", "                print(\"1. Checking output shapes...\")\n", "                for name, tensor in outputs.items():\n", "                    inspection_results['output_shapes'][name] = list(tensor.shape)\n", "                    check(len(tensor.shape) == 4, f\"{name} has 4 dimensions (B,C,H,W)\")\n", "                # Channel checks\n", "                check(outputs['sem_out'].shape[1] == 1, \"Semantic output has 1 channel\")\n", "                check(outputs['sdt_out'].shape[1] == 11, \"SDT output has 11 channels (10 bins + background)\")\n", "                check(outputs['skeleton_out'].shape[1] == 1, \"Skeleton output has 1 channel\")\n", "                check(outputs['hm_out'].shape[1] == 1, \"Centroid output has 1 channel\")\n", "                check(outputs['flow_out'].shape[1] == 2, \"Flow output has 2 channels (y,x)\")\n", "\n", "            # 2. Check value ranges\n", "            print(\"\\n2. Checking value ranges...\")\n", "            for name, tensor in outputs.items():\n", "                if name == 'sdt_out':\n", "                    continue  # Logits, any value\n", "                elif name == 'flow_out':\n", "                    flow_mag = torch.sqrt(torch.sum(tensor**2, dim=1, keepdim=True) + 1e-8)\n", "                    max_mag = flow_mag.max().item()\n", "                    check(max_mag <= 1.51, f\"Flow magnitude <= 1.5 (max: {max_mag:.3f})\")\n", "                    inspection_results['flow_validity'].append(max_mag)\n", "                else:\n", "                    min_val = tensor.min().item()\n", "                    max_val = tensor.max().item()\n", "                    if name in ['sem_out', 'hm_out', 'skeleton_out']:\n", "                        check(0 <= min_val <= max_val <= 1, f\"{name} in [0,1] (min: {min_val:.3f}, max: {max_val:.3f})\")\n", "                    inspection_results['value_ranges'].setdefault(name, []).extend([min_val, max_val])\n", "\n", "            # 3. Check SDT properties\n", "            print(\"\\n3. Checking SDT properties...\")\n", "            sdt_probs = F.softmax(outputs['sdt_out'], dim=1)\n", "            bin_centers = (torch.arange(0, sdt_probs.shape[1], dtype=torch.float32, device=sdt_probs.device) + 0.5) / sdt_probs.shape[1]\n", "            bin_centers = bin_centers.view(1, -1, 1, 1)\n", "            sdt_recon = torch.sum(sdt_probs * bin_centers, dim=1, keepdim=True)\n", "\n", "            for i in range(min(2, batch_size)):\n", "                gt_skeleton = targets[i:i+1, 2:3] > 0.5\n", "                gt_boundary = targets[i:i+1, 6:7] > 0.5  # Index 6 is boundary\n", "                pred_sdt = sdt_recon[i:i+1]\n", "\n", "                if gt_skeleton.sum() > 0:\n", "                    skel_sdt_vals = pred_sdt[gt_skeleton]\n", "                    if skel_sdt_vals.numel() > 0:\n", "                        skel_sdt_mean = skel_sdt_vals.mean().item()\n", "                        check(skel_sdt_mean < 0.5, f\"Sample {i}: SDT low at skeleton (mean: {skel_sdt_mean:.3f})\")\n", "                    else:\n", "                        print(f\"    Warning: No valid skeleton pixels for sample {i}.\")\n", "                else:\n", "                    skel_sdt_mean = 0\n", "\n", "                if gt_boundary.sum() > 0:\n", "                    bound_sdt_vals = pred_sdt[gt_boundary]\n", "                    non_bound_sdt_vals = pred_sdt[~gt_boundary]\n", "                    if bound_sdt_vals.numel() > 0 and non_bound_sdt_vals.numel() > 0:\n", "                        bound_sdt_mean = bound_sdt_vals.mean().item()\n", "                        non_bound_sdt_mean = non_bound_sdt_vals.mean().item()\n", "                        check(bound_sdt_mean > non_bound_sdt_mean, \n", "                              f\"Sample {i}: SDT higher at boundary (bound: {bound_sdt_mean:.3f}, non-bound: {non_bound_sdt_mean:.3f})\")\n", "                        inspection_results['sdt_properties'].append({\n", "                            'sample': batch_idx*batch_size + i,\n", "                            'skel_sdt': skel_sdt_mean,\n", "                            'bound_sdt': bound_sdt_mean,\n", "                            'non_bound_sdt': non_bound_sdt_mean\n", "                        })\n", "\n", "            # 4. Check Centroid Localization & Smoothness\n", "            print(\"\\n4. Checking centroid localization and smoothness...\")\n", "            for i in range(min(2, batch_size)):\n", "                pred_hm = torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy().astype(np.float32)\n", "                gt_hm = targets[i, 3].cpu().numpy()  # Index 3 is centroid map\n", "                pred_semantic = torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy() > 0.5\n", "\n", "                # --- Centroid within semantic ---\n", "                peaks_pred = (pred_hm > 0.3) & (maximum_filter(pred_hm, size=3) == pred_hm)\n", "                if peaks_pred.sum() > 0:\n", "                    centroid_in_semantic = np.logical_and(peaks_pred, pred_semantic).sum()\n", "                    total_centroids = peaks_pred.sum()\n", "                    accuracy = centroid_in_semantic / total_centroids\n", "                    check(accuracy > 0.8, f\"Sample {i}: Centroids within semantic ({accuracy*100:.1f}%)\")\n", "                    inspection_results['centroid_accuracy'].append(accuracy)\n", "                else:\n", "                    inspection_results['centroid_accuracy'].append(0.0)\n", "\n", "                # --- Smoothness Check: Compare to Gaussian ---\n", "                # Compute gradient magnitude of heatmap\n", "                grad_y, grad_x = np.gradient(pred_hm)\n", "                grad_mag = np.hypot(grad_y, grad_x)\n", "                # Smooth heatmaps have low max gradient\n", "                max_grad = grad_mag.max()\n", "                check(max_grad < 0.3, f\"Sample {i}: Centroid heatmap smooth (max grad: {max_grad:.3f})\")\n", "                inspection_results['centroid_smoothness'].append(max_grad)\n", "\n", "            # 5. Simplified Instance Segmentation\n", "            print(\"\\n5. Checking simplified instance segmentation...\")\n", "            for i in range(min(2, batch_size)):\n", "                pred_sem = torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy().astype(np.float32)\n", "                pred_sdt = sdt_recon[i, 0].cpu().numpy().astype(np.float32)\n", "                pred_hm = torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy().astype(np.float32)\n", "\n", "                mask = (pred_sem > 0.5).astype(np.float32)\n", "                response = pred_hm * mask\n", "                local_max = maximum_filter(response, size=3) == response\n", "                peaks = local_max & (response > 0.3)\n", "                coords = np.column_stack(np.where(peaks))\n", "\n", "                if len(coords) > 0:\n", "                    seeds = np.zeros_like(pred_sem, dtype=np.int32)\n", "                    for idx, (y, x) in enumerate(coords, 1):\n", "                        seeds[y, x] = idx\n", "                    binary_mask = (pred_sem > 0.5).astype(np.uint8)\n", "                    try:\n", "                        instance_labels = watershed(-pred_sdt, seeds, mask=binary_mask)\n", "                        num_instances = len(np.unique(instance_labels)) - 1\n", "                        check(num_instances > 0, f\"Sample {i}: Instance seg successful ({num_instances})\")\n", "                        inspection_results['instance_segmentation'].append(num_instances)\n", "                    except Exception as e:\n", "                        print(f\"    Failed: {e}\")\n", "                        inspection_results['instance_segmentation'].append(0)\n", "                else:\n", "                    inspection_results['instance_segmentation'].append(0)\n", "\n", "            # 6. Visualization\n", "            print(\"\\n6. Generating visualization...\")\n", "            try:\n", "                fig, axes = plt.subplots(3, 5, figsize=(25, 15))  # Added one more column\n", "                sample_idx = 0\n", "\n", "                # Input\n", "                axes[0,0].imshow(images[sample_idx, 0].cpu(), cmap='gray')\n", "                axes[0,0].set_title('Input Image')\n", "                axes[0,0].axis('off')\n", "\n", "                # Semantic\n", "                axes[0,1].imshow(torch.sigmoid(outputs['sem_out'][sample_idx, 0]).cpu(), cmap='viridis')\n", "                axes[0,1].set_title('Predicted Semantic')\n", "                axes[0,1].axis('off')\n", "\n", "                # SDT\n", "                axes[0,2].imshow(sdt_recon[sample_idx, 0].cpu(), cmap='magma')\n", "                axes[0,2].set_title('SDT (Reconstructed)')\n", "                axes[0,2].axis('off')\n", "\n", "                # Skeleton\n", "                axes[0,3].imshow(torch.sigmoid(outputs['skeleton_out'][sample_idx, 0]).cpu(), cmap='bone')\n", "                axes[0,3].set_title('Predicted Skeleton')\n", "                axes[0,3].axis('off')\n", "\n", "                # Predicted Centroid\n", "                axes[0,4].imshow(torch.sigmoid(outputs['hm_out'][sample_idx, 0]).cpu(), cmap='hot')\n", "                axes[0,4].set_title('Predicted <PERSON><PERSON> (<PERSON><PERSON><PERSON>)')\n", "                axes[0,4].axis('off')\n", "\n", "                # <PERSON> Se<PERSON>tic, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>id, Flow Mag\n", "                axes[1,0].imshow(targets[sample_idx, 0].cpu(), cmap='viridis')\n", "                axes[1,0].set_title('GT Semantic')\n", "                axes[1,0].axis('off')\n", "\n", "                axes[1,1].imshow(targets[sample_idx, 1].cpu(), cmap='magma')\n", "                axes[1,1].set_title('GT SDT')\n", "                axes[1,1].axis('off')\n", "\n", "                axes[1,2].imshow(targets[sample_idx, 6].cpu(), cmap='gray')  # Boundary\n", "                axes[1,2].set_title('GT Boundary')\n", "                axes[1,2].axis('off')\n", "\n", "                axes[1,3].imshow(targets[sample_idx, 3].cpu(), cmap='hot')  # Centroid\n", "                axes[1,3].set_title('GT Centroid (Gaussian)')\n", "                axes[1,3].axis('off')\n", "\n", "                flow_mag = torch.sqrt(torch.sum(outputs['flow_out'][sample_idx]**2, dim=0) + 1e-8)\n", "                axes[1,4].imshow(flow_mag.cpu(), cmap='plasma')\n", "                axes[1,4].set_title('Flow Magnitude')\n", "                axes[1,4].axis('off')\n", "\n", "                # Instance Segmentation, GT Overlay, Centroid Overlay, Smoothness Check, Empty\n", "                pred_sem_np = torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy().astype(np.float32)\n", "                pred_sdt_np = sdt_recon[i, 0].cpu().numpy().astype(np.float32)\n", "                pred_hm_np = torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy().astype(np.float32)\n", "\n", "                mask_np = (pred_sem_np > 0.5).astype(np.uint8)\n", "                response_np = pred_hm_np * mask_np\n", "                peaks_np = (response_np > 0.3) & (maximum_filter(response_np, size=3) == response_np)\n", "                coords_np = np.column_stack(np.where(peaks_np))\n", "\n", "                if len(coords_np) > 0:\n", "                    seeds_np = np.zeros_like(pred_sem_np, dtype=np.int32)\n", "                    for idx, (y, x) in enumerate(coords_np, 1):\n", "                        seeds_np[y, x] = idx\n", "                    instance_labels = watershed(-pred_sdt_np, seeds_np, mask=mask_np)\n", "                else:\n", "                    instance_labels = np.zeros_like(pred_sem_np)\n", "\n", "                instance_viz = label2rgb(instance_labels, bg_label=0, alpha=0.7)\n", "                axes[2,0].imshow(instance_viz)\n", "                axes[2,0].set_title('Instance Segmentation')\n", "                axes[2,0].axis('off')\n", "\n", "                # GT overlay\n", "                axes[2,1].imshow(targets[sample_idx, 0].cpu(), cmap='gray')\n", "                gt_centroids = (targets[sample_idx, 3].cpu().numpy() > 0.5)\n", "                if gt_centroids.any():\n", "                    yc, xc = np.where(gt_centroids)\n", "                    axes[2,1].scatter(xc, yc, c='red', s=30, marker='x')\n", "                axes[2,1].set_title('GT Semantic + Centroids')\n", "                axes[2,1].axis('off')\n", "\n", "                # Predicted centroids\n", "                axes[2,2].imshow(images[sample_idx, 0].cpu(), cmap='gray')\n", "                if len(coords_np) > 0:\n", "                    axes[2,2].scatter(coords_np[:, 1], coords_np[:, 0], c='cyan', s=30, marker='o', alpha=0.8)\n", "                axes[2,2].set_title('Detected Centroids')\n", "                axes[2,2].axis('off')\n", "\n", "                # Gradient magnitude of predicted centroid map\n", "                gy, gx = np.gradient(pred_hm_np)\n", "                grad_mag_vis = np.hypot(gy, gx)\n", "                im = axes[2,3].imshow(grad_mag_vis, cmap='viridis', vmin=0, vmax=0.3)\n", "                plt.colorbar(im, ax=axes[2,3])\n", "                axes[2,3].set_title('Centroid Grad Magnitude')\n", "                axes[2,3].axis('off')\n", "\n", "                axes[2,4].axis('off')  # Empty\n", "\n", "                plt.tight_layout()\n", "                plt.suptitle(f'Model Inspection - Batch {batch_idx}', fontsize=16)\n", "                plt.subplots_adjust(top=0.93)\n", "                plt.savefig(os.path.join(save_dir, f\"model_inspection_batch_{batch_idx}.png\"), dpi=150, bbox_inches='tight')\n", "                plt.close()\n", "            except Exception as e:\n", "                print(f\"  Visualization error: {e}\")\n", "\n", "    # Summary\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"MODEL INSPECTION SUMMARY\")\n", "    print(\"=\"*60)\n", "    print(f\"Passed checks: {inspection_results['passed_checks']}/{inspection_results['total_checks']}\")\n", "    if inspection_results['flow_validity']:\n", "        print(f\"Max flow magnitude: {max(inspection_results['flow_validity']):.3f}\")\n", "    if inspection_results['centroid_accuracy']:\n", "        avg_acc = np.mean(inspection_results['centroid_accuracy'])\n", "        print(f\"Average centroid accuracy: {avg_acc*100:.1f}%\")\n", "    if inspection_results['centroid_smoothness']:\n", "        avg_smooth = np.mean(inspection_results['centroid_smoothness'])\n", "        print(f\"Average centroid gradient: {avg_smooth:.3f} (lower = smoother)\")\n", "    if inspection_results['instance_segmentation']:\n", "        avg_inst = np.mean(inspection_results['instance_segmentation'])\n", "        print(f\"Avg instances per sample: {avg_inst:.1f}\")\n", "    success_rate = inspection_results['passed_checks'] / inspection_results['total_checks']\n", "    if success_rate >= 0.9:\n", "        print(\"\\n🎉 INSPECTION PASSED: Model outputs are consistent and correct.\")\n", "    else:\n", "        print(\"\\n⚠️  INSPECTION FAILED: Some checks did not pass. Please review.\")\n", "\n", "\n", "\n", "    return inspection_results\n", "\n", "# Example usage (uncomment to run):\n", "# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "# model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48).to(device)\n", "# # Load a trained model if available\n", "# # checkpoint = torch.load('path/to/best_model.pth')\n", "# # model.load_state_dict(checkpoint['model_state_dict'])\n", "# \n", "# # Create data loaders (replace with your actual paths)\n", "# # train_loader, val_loader = create_skeleton_aware_data_loaders(image_paths, mask_paths)\n", "# \n", "# # Run inspection\n", "# # results = inspect_skeleton_aware_model(model, val_loader, device, num_batches=2)\n", "\n", "print(\"inspect_skeleton_aware_model function updated with fixes.\")"]}, {"cell_type": "code", "execution_count": 27, "id": "93cf1106", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ State-of-the-Art training function 'train_skeleton_aware_model_sota' with dynamic curriculum is ready for use.\n"]}], "source": ["# Cell: State-of-the-Art Improved Training Function with Resume, Early Stopping, and Curriculum Learning\n", "import os\n", "import torch\n", "import torch.nn as nn\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from tqdm import tqdm\n", "\n", "# --- AMP import: compatible with both PyTorch 1.x and 2.x ---\n", "try:\n", "    from torch.amp import GradScaler, autocast  # PyTorch 2.0+\n", "except ImportError:\n", "    from torch.cuda.amp import GradScaler, autocast  # PyTorch < 2.0\n", "\n", "\n", "def train_skeleton_aware_model_sota(model, train_loader, val_loader, num_epochs=300,\n", "                                    device='cuda', model_dir='./',\n", "                                    resume=True, early_stopping_patience=30):\n", "    \"\"\"\n", "    State-of-the-Art optimized training loop for SkeletonAwareSpotDetector.\n", "    Improvements:\n", "    - Robust resume capability with full state saving/loading.\n", "    - Early stopping to prevent overfitting and save time.\n", "    - Improved optimizer (AdamW) with potential fused implementation.\n", "    - Better LR scheduler (CosineAnnealingWarmRestarts).\n", "    - Enhanced gradient clipping.\n", "    - Comprehensive logging and visualization.\n", "    - Dynamic curriculum learning via loss function weight updates.\n", "    \"\"\"\n", "    os.makedirs(model_dir, exist_ok=True)\n", "    print(f\"Model outputs will be saved to: {model_dir}\")\n", "\n", "    # --- 1. Loss Function ---\n", "    criterion = SkeletonAwareLoss()\n", "    print(\"Using loss function: SkeletonAwareLoss\")\n", "\n", "    # --- 2. Optimizer Setup (Fixed) ---\n", "    base_lr = 1e-3\n", "    optimizer_kwargs = {\n", "        'lr': base_lr,\n", "        'weight_decay': 1e-2,\n", "        'betas': (0.9, 0.999)\n", "    }\n", "    # Direct fused AdamW creation\n", "    try:\n", "        optimizer = torch.optim.AdamW(model.parameters(), fused=True, **optimizer_kwargs)\n", "        print(\"✅ Using fused AdamW optimizer.\")\n", "    except Exception as e:\n", "        optimizer = torch.optim.AdamW(model.parameters(), **optimizer_kwargs)\n", "        print(f\"⚠️ Fused AdamW failed ({e}), using standard AdamW.\")\n", "\n", "    # --- 3. Better Learning Rate Schedule ---\n", "    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(\n", "        optimizer,\n", "        T_0=20,           # Restart every 20 epochs\n", "        T_mult=1,         # Keep same cycle length\n", "        eta_min=1e-6      # Lower minimum LR\n", "    )\n", "    print(\"Using LR Scheduler: CosineAnnealingWarmRestarts\")\n", "\n", "    # --- 4. AM<PERSON> Scaler ---\n", "    scaler = GradScaler()\n", "    print(\"Using Automatic Mixed Precision (AMP).\")\n", "\n", "    # --- 5. <PERSON> & Device ---\n", "    model.to(device)\n", "    print(f\"Model moved to device: {device}\")\n", "\n", "    # --- 6. State for Resuming and Early Stopping ---\n", "    start_epoch = 0\n", "    best_loss = float('inf')\n", "    epochs_since_improvement = 0\n", "\n", "    # Initialize loss tracking\n", "    initial_train_losses, initial_val_losses = [], []\n", "    initial_train_component_losses = {'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': []}\n", "    initial_val_component_losses = {'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': []}\n", "\n", "    # --- 7. <PERSON><PERSON><PERSON><PERSON> LOGIC ---\n", "    best_model_path = os.path.join(model_dir, 'best_model.pth')\n", "    final_model_path = os.path.join(model_dir, 'final_model.pth')\n", "\n", "    if resume and os.path.isfile(best_model_path):\n", "        print(f\"Attempting to resume training from {best_model_path}...\")\n", "        try:\n", "            checkpoint = torch.load(best_model_path, map_location=device, weights_only=False)\n", "            model.load_state_dict(checkpoint['model_state_dict'])\n", "            print(\"✅ Model state loaded successfully.\")\n", "\n", "            if 'optimizer_state_dict' in checkpoint:\n", "                optimizer.load_state_dict(checkpoint['optimizer_state_dict'])\n", "                print(\"✅ Optimizer state loaded successfully.\")\n", "            else:\n", "                print(\"⚠️ Warning: 'optimizer_state_dict' not found in checkpoint. Starting optimizer from scratch.\")\n", "\n", "            if 'scheduler_state_dict' in checkpoint:\n", "                try:\n", "                    scheduler.load_state_dict(checkpoint['scheduler_state_dict'])\n", "                    print(\"✅ Scheduler state loaded successfully.\")\n", "                except Exception as e:\n", "                    print(f\"⚠️ Warning: Failed to load scheduler state: {e}. Scheduler will restart.\")\n", "            else:\n", "                print(\"⚠️ Warning: 'scheduler_state_dict' not found in checkpoint. Scheduler will restart.\")\n", "\n", "            start_epoch = checkpoint.get('epoch', 0) + 1\n", "            best_loss = checkpoint.get('loss', float('inf'))\n", "            epochs_since_improvement = checkpoint.get('epochs_since_improvement', 0)\n", "\n", "            if 'train_losses' in checkpoint:\n", "                initial_train_losses = checkpoint['train_losses'][:start_epoch]\n", "            if 'val_losses' in checkpoint:\n", "                initial_val_losses = checkpoint['val_losses'][:start_epoch]\n", "\n", "            print(f\"✅ Resumed from epoch {start_epoch}, best val loss: {best_loss:.6f}, \"\n", "                  f\"epochs since improvement: {epochs_since_improvement}\")\n", "        except Exception as e:\n", "            print(f\"❌ Failed to resume: {e}. Starting from scratch.\")\n", "            start_epoch = 0\n", "            best_loss = float('inf')\n", "            epochs_since_improvement = 0\n", "    else:\n", "        if resume:\n", "            print(f\"No checkpoint found at {best_model_path}. Starting from scratch.\")\n", "        else:\n", "            print(\"Resume disabled. Starting from scratch.\")\n", "\n", "    # --- 8. Initialize loss tracking ---\n", "    train_losses = initial_train_losses[:]\n", "    val_losses = initial_val_losses[:]\n", "    train_component_losses = {k: v[:] for k, v in initial_train_component_losses.items()}\n", "    val_component_losses = {k: v[:] for k, v in initial_val_component_losses.items()}\n", "\n", "    # --- 9. <PERSON> Loop ---\n", "    print(f\"\\n--- Starting/Resuming Training from epoch {start_epoch + 1} to {num_epochs} ---\")\n", "\n", "    for epoch in range(start_epoch, num_epochs):\n", "        # --- Curriculum Learning: Dynamic Weight Scheduling ---\n", "        if hasattr(criterion, 'set_epoch'):\n", "            criterion.set_epoch(epoch)\n", "\n", "        # Compute curriculum weights\n", "        sdt_weight = min(1.0, epoch / 20.0)      # Start earlier, converge faster\n", "        flow_weight = min(1.0, max(0.0, (epoch - 5) / 15.0))  # Start flow at epoch 5\n", "        cent_weight = 1.0  # Can be increased if needed — Gaussian centroids are stable\n", "\n", "        # Update loss function weights dynamically\n", "        if hasattr(criterion, 'w_sdt'):\n", "            criterion.w_sdt = sdt_weight\n", "        if hasattr(criterion, 'w_flow'):\n", "            criterion.w_flow = flow_weight\n", "        if hasattr(criterion, 'w_cent'):\n", "            criterion.w_cent = cent_weight\n", "\n", "        print(f\"📚 Curriculum: SDT={sdt_weight:.3f}, Flow={flow_weight:.3f}, Cent={cent_weight:.3f}\")\n", "\n", "        # --- Training Phase ---\n", "        model.train()\n", "        epoch_train_losses = []\n", "        epoch_train_components = {k: 0.0 for k in train_component_losses}\n", "\n", "        train_pbar = tqdm(train_loader, desc=f\"Epoch {epoch+1}/{num_epochs} [Train]\")\n", "        for images, targets in train_pbar:\n", "            images = images.float().to(device, non_blocking=True)\n", "            targets = targets.float().to(device, non_blocking=True)\n", "\n", "            optimizer.zero_grad(set_to_none=True)\n", "\n", "            with autocast(device_type=device.type):\n", "                outputs = model(images)\n", "                loss, loss_dict = criterion(outputs, targets)\n", "\n", "            if not torch.isnan(loss):\n", "                scaler.scale(loss).backward()\n", "                scaler.unscale_(optimizer)\n", "                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)\n", "                scaler.step(optimizer)\n", "                scaler.update()\n", "                scheduler.step()\n", "\n", "                epoch_train_losses.append(loss.item())\n", "                for k in epoch_train_components:\n", "                    if k in loss_dict:\n", "                        epoch_train_components[k] += loss_dict[k].item()\n", "\n", "                if len(epoch_train_losses) % 10 == 0 or len(epoch_train_losses) == len(train_loader):\n", "                    current_lr = optimizer.param_groups[0]['lr']\n", "                    train_pbar.set_postfix({\n", "                        'Loss': f'{loss.item():.4f}',\n", "                        'LR': f'{current_lr:.2e}',\n", "                        'Grad_Norm': f'{grad_norm:.2f}'\n", "                    })\n", "\n", "        # --- Average Training Metrics ---\n", "        avg_train_loss = np.mean(epoch_train_losses) if epoch_train_losses else float('inf')\n", "        for k in epoch_train_components:\n", "            epoch_train_components[k] /= len(train_loader) if len(train_loader) > 0 else 1\n", "            train_component_losses[k].append(epoch_train_components[k])\n", "        train_losses.append(avg_train_loss)\n", "\n", "        # --- Validation Phase ---\n", "        model.eval()\n", "        epoch_val_losses = []\n", "        epoch_val_components = {k: 0.0 for k in val_component_losses}\n", "\n", "        with torch.no_grad():\n", "            val_pbar = tqdm(val_loader, desc=f\"Epoch {epoch+1}/{num_epochs} [Val]\", leave=False)\n", "            for images, targets in val_pbar:\n", "                images = images.float().to(device, non_blocking=True)\n", "                targets = targets.float().to(device, non_blocking=True)\n", "\n", "                with autocast(device_type=device.type):\n", "                    outputs = model(images)\n", "                    loss, loss_dict = criterion(outputs, targets)\n", "\n", "                if not torch.isnan(loss):\n", "                    epoch_val_losses.append(loss.item())\n", "                    for k in epoch_val_components:\n", "                        if k in loss_dict:\n", "                            epoch_val_components[k] += loss_dict[k].item()\n", "\n", "                if len(epoch_val_losses) % 10 == 0 or len(epoch_val_losses) == len(val_loader):\n", "                    val_pbar.set_postfix({'Loss': f'{loss.item():.4f}'})\n", "\n", "        # --- Average Validation Metrics ---\n", "        avg_val_loss = np.mean(epoch_val_losses) if epoch_val_losses else float('inf')\n", "        for k in epoch_val_components:\n", "            epoch_val_components[k] /= len(val_loader) if len(val_loader) > 0 else 1\n", "            val_component_losses[k].append(epoch_val_components[k])\n", "        val_losses.append(avg_val_loss)\n", "\n", "        # --- Epoch Summary ---\n", "        current_lr = optimizer.param_groups[0]['lr']\n", "        print(f\"Epoch {epoch+1} Summary:\")\n", "        print(f\"  Train Loss: {avg_train_loss:.6f} | Val Loss: {avg_val_loss:.6f} | LR: {current_lr:.2e}\")\n", "        train_comp_str = \" | \".join([f\"{k}: {v[-1]:.4f}\" for k, v in train_component_losses.items() if v])\n", "        val_comp_str = \" | \".join([f\"{k}: {v[-1]:.4f}\" for k, v in val_component_losses.items() if v])\n", "        print(f\"  Train Components: {train_comp_str}\")\n", "        print(f\"  Val Components:   {val_comp_str}\")\n", "\n", "        # --- 10. Early Stopping & Best Model Saving ---\n", "        if avg_val_loss < best_loss:\n", "            best_loss = avg_val_loss\n", "            epochs_since_improvement = 0\n", "            best_checkpoint = {\n", "                'model_state_dict': model.state_dict(),\n", "                'optimizer_state_dict': optimizer.state_dict(),\n", "                'scheduler_state_dict': scheduler.state_dict(),\n", "                'epoch': epoch,\n", "                'loss': best_loss,\n", "                'epochs_since_improvement': epochs_since_improvement,\n", "                'train_losses': train_losses,\n", "                'val_losses': val_losses,\n", "            }\n", "            torch.save(best_checkpoint, best_model_path)\n", "            print(f\"  ✅ New best model saved (Val Loss: {best_loss:.6f})\")\n", "        else:\n", "            epochs_since_improvement += 1\n", "            print(f\"  ⏱️  Epochs since last improvement: {epochs_since_improvement}\")\n", "\n", "        if epochs_since_improvement >= early_stopping_patience:\n", "            print(f\"\\n⚠️ Early stopping triggered after {epoch + 1} epochs (patience {early_stopping_patience}).\")\n", "            break\n", "\n", "        # --- 11. Periodic Visualization ---\n", "        if epoch % max(1, num_epochs // 20) == 0 or epoch == num_epochs - 1 or epoch == start_epoch:\n", "            try:\n", "                images, targets = next(iter(val_loader))\n", "                images = images.float().to(device)\n", "                with torch.no_grad():\n", "                    with autocast(device_type=device.type):\n", "                        outputs = model(images)\n", "                    semantic = torch.sigmoid(outputs['sem_out'])[0, 0].cpu().numpy()\n", "                    sdt_probs = torch.nn.functional.softmax(outputs['sdt_out'], dim=1)\n", "                    num_bins = sdt_probs.shape[1]\n", "                    bin_centers = (torch.arange(0, num_bins, device=sdt_probs.device, dtype=torch.float32) + 0.5) / num_bins\n", "                    bin_centers = bin_centers.view(1, -1, 1, 1)\n", "                    sdt = torch.sum(sdt_probs * bin_centers, dim=1)[0].cpu().numpy()\n", "                    skeleton = torch.sigmoid(outputs['skeleton_out'])[0, 0].cpu().numpy()\n", "                    centroid_map = torch.sigmoid(outputs['hm_out'])[0, 0].cpu().numpy()\n", "\n", "                fig, axes = plt.subplots(1, 5, figsize=(25, 5))\n", "                axes[0].imshow(images[0, 0].cpu(), cmap='gray')\n", "                axes[0].set_title('Input Image')\n", "                axes[0].axis('off')\n", "                axes[1].imshow(semantic, cmap='viridis')\n", "                axes[1].set_title('Semantic Mask')\n", "                axes[1].axis('off')\n", "                im2 = axes[2].imshow(sdt, cmap='magma')\n", "                axes[2].set_title('SDT (Reconstructed)')\n", "                plt.colorbar(im2, ax=axes[2])\n", "                axes[3].imshow(skeleton, cmap='bone')\n", "                axes[3].set_title('Skeleton')\n", "                axes[3].axis('off')\n", "                axes[4].imshow(centroid_map, cmap='hot')\n", "                axes[4].set_title('Centroid Heatmap')\n", "                axes[4].axis('off')\n", "                plt.suptitle(f'Training Progress - Epoch {epoch+1}')\n", "                plt.tight_layout()\n", "                progress_plot_path = os.path.join(model_dir, f'progress_epoch_{epoch+1}.png')\n", "                plt.savefig(progress_plot_path, dpi=150, bbox_inches='tight')\n", "                plt.close(fig)\n", "                print(f\"  📊 Progress plot saved to {progress_plot_path}\")\n", "            except Exception as e:\n", "                print(f\"  ⚠️ Visualization error for epoch {epoch+1}: {e}\")\n", "\n", "    # --- 12. Finalization ---\n", "    print(\"\\n--- Training Completed ---\")\n", "    final_checkpoint = {\n", "        'model_state_dict': model.state_dict(),\n", "        'optimizer_state_dict': optimizer.state_dict(),\n", "        'scheduler_state_dict': scheduler.state_dict(),\n", "        'epoch': len(train_losses) - 1,\n", "        'loss': val_losses[-1] if val_losses else float('inf'),\n", "        'epochs_since_improvement': epochs_since_improvement,\n", "        'train_losses': train_losses,\n", "        'val_losses': val_losses,\n", "    }\n", "    torch.save(final_checkpoint, final_model_path)\n", "    print(f\"💾 Final model checkpoint saved to {final_model_path}\")\n", "\n", "    # --- 13. Plot Final Loss Curves ---\n", "    try:\n", "        plt.figure(figsize=(12, 5))\n", "        plt.subplot(1, 2, 1)\n", "        epochs_plotted = range(1, len(train_losses) + 1)\n", "        plt.plot(epochs_plotted, train_losses, label='Train Loss')\n", "        plt.plot(epochs_plotted, val_losses, label='Val Loss')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Loss')\n", "        plt.title('Overall Training & Validation Loss')\n", "        plt.legend()\n", "        plt.grid(True)\n", "\n", "        plt.subplot(1, 2, 2)\n", "        for k, v in train_component_losses.items():\n", "            if v:\n", "                plt.plot(epochs_plotted, v, label=f'Train {k}')\n", "        for k, v in val_component_losses.items():\n", "            if v:\n", "                val_comp_aligned = v[:len(epochs_plotted)]\n", "                plt.plot(epochs_plotted, val_comp_aligned, label=f'Val {k}', linestyle='--')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Loss')\n", "        plt.title('Component Losses')\n", "        plt.legend()\n", "        plt.grid(True)\n", "        plt.tight_layout()\n", "        loss_curve_path = os.path.join(model_dir, 'final_loss_curves.png')\n", "        plt.savefig(loss_curve_path)\n", "        plt.close()\n", "        print(f\"📊 Final loss curves plot saved to {loss_curve_path}\")\n", "    except Exception as e:\n", "        print(f\"⚠️ Error plotting final loss curves: {e}\")\n", "\n", "    print(f\"\\n🎉 Training finished. Best validation loss: {best_loss:.6f}\")\n", "    return model, train_losses, val_losses\n", "\n", "\n", "print(\"✅ State-of-the-Art training function 'train_skeleton_aware_model_sota' with dynamic curriculum is ready for use.\")"]}, {"cell_type": "code", "execution_count": 28, "id": "d0c3272d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fused optimizers available: True\n"]}], "source": ["print(f\"Fused optimizers available: {hasattr(torch.optim.AdamW, '__init__') and 'fused' in torch.optim.AdamW.__init__.__code__.co_varnames}\")\n"]}, {"cell_type": "code", "execution_count": 29, "id": "d2999519", "metadata": {}, "outputs": [], "source": ["# # Cell 5: Training and Evaluation - Corrected and Improved\n", "# def train_skeleton_aware_model_optimized(model, train_loader, val_loader, num_epochs=50, \n", "#                                          device='cuda', model_dir='./'):\n", "#     os.makedirs(model_dir, exist_ok=True)\n", "#     criterion = SkeletonAwareLoss()\n", "#     base_lr = 1e-3\n", "#     optimizer = torch.optim.AdamW(model.parameters(), lr=base_lr, weight_decay=1e-4, betas=(0.9, 0.999))\n", "#     scheduler = torch.optim.lr_scheduler.OneCycleLR(\n", "#         optimizer, max_lr=base_lr, epochs=num_epochs,\n", "#         steps_per_epoch=len(train_loader), pct_start=0.1, anneal_strategy='cos'\n", "#     )\n", "#     scaler = GradScaler()\n", "#     model.to(device)\n", "#     best_loss = float('inf')\n", "#     train_losses, val_losses = [], []\n", "#     train_component_losses = {'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': []}\n", "#     val_component_losses = {'semantic': [], 'sdt': [], 'skeleton': [], 'centroid': [], 'flow': []}\n", "\n", "#     for epoch in range(num_epochs):\n", "#         # Training\n", "#         model.train()\n", "#         epoch_train_losses = []\n", "#         epoch_train_components = {k: 0 for k in train_component_losses}\n", "#         pbar = tqdm(train_loader, desc=f\"Epoch {epoch+1}/{num_epochs}\")\n", "#         for images, targets in pbar:\n", "#             images = images.float().to(device, non_blocking=True)\n", "#             targets = targets.float().to(device, non_blocking=True)\n", "#             optimizer.zero_grad(set_to_none=True)\n", "#             with autocast('cuda'):\n", "#                 outputs = model(images)\n", "#                 loss, loss_dict = criterion(outputs, targets)\n", "#             if not torch.isnan(loss):\n", "#                 scaler.scale(loss).backward()\n", "#                 scaler.unscale_(optimizer)\n", "#                 torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)\n", "#                 scaler.step(optimizer)\n", "#                 scaler.update()\n", "#                 scheduler.step()\n", "#                 epoch_train_losses.append(loss.item())\n", "#                 for k in epoch_train_components:\n", "#                     if k in loss_dict: epoch_train_components[k] += loss_dict[k].item()\n", "#             pbar.set_postfix({'Loss': f'{loss.item():.4f}' if not torch.isnan(loss) else 'NaN'})\n", "\n", "#         for k in epoch_train_components:\n", "#             epoch_train_components[k] /= len(train_loader)\n", "#             train_component_losses[k].append(epoch_train_components[k])\n", "#         train_losses.append(np.mean(epoch_train_losses))\n", "\n", "#         # Validation\n", "#         model.eval()\n", "#         epoch_val_losses = []\n", "#         epoch_val_components = {k: 0 for k in val_component_losses}\n", "#         with torch.no_grad():\n", "#             for images, targets in val_loader:\n", "#                 images = images.float().to(device, non_blocking=True)\n", "#                 targets = targets.float().to(device, non_blocking=True)\n", "#                 with autocast('cuda'):\n", "#                     outputs = model(images)\n", "#                     loss, loss_dict = criterion(outputs, targets)\n", "#                 if not torch.isnan(loss):\n", "#                     epoch_val_losses.append(loss.item())\n", "#                     for k in epoch_val_components:\n", "#                         if k in loss_dict: epoch_val_components[k] += loss_dict[k].item()\n", "\n", "#         for k in epoch_val_components:\n", "#             epoch_val_components[k] /= len(val_loader)\n", "#             val_component_losses[k].append(epoch_val_components[k])\n", "#         val_loss = np.mean(epoch_val_losses) if epoch_val_losses else float('inf')\n", "#         val_losses.append(val_loss)\n", "\n", "#         print(f\"Epoch {epoch+1}: Train Loss: {train_losses[-1]:.4f} | Val Loss: {val_losses[-1]:.4f}\")\n", "#         print(f\"  LR: {optimizer.param_groups[0]['lr']:.6e}\")\n", "\n", "#         if val_losses[-1] < best_loss:\n", "#             best_loss = val_losses[-1]\n", "#             torch.save({'model_state_dict': model.state_dict(), 'loss': best_loss},\n", "#                        os.path.join(model_dir, 'best_model.pth'))\n", "#             print(f\"✅ Best model saved (val_loss={best_loss:.4f})\")\n", "\n", "#         # Visualization (simplified)\n", "#         if epoch % 10 == 0 or epoch == num_epochs - 1:\n", "#             try:\n", "#                 images, targets = next(iter(val_loader))\n", "#                 images = images.float().to(device)\n", "#                 with torch.no_grad():\n", "#                     with autocast('cuda'):\n", "#                         outputs = model(images)\n", "#                 semantic = torch.sigmoid(outputs['sem_out'])[0, 0].cpu().numpy()\n", "#                 sdt_probs = F.softmax(outputs['sdt_out'], dim=1)\n", "#                 # --- IMPROVED SDT Reconstruction ---\n", "#                 bin_centers = (torch.arange(0, sdt_probs.shape[1], device=sdt_probs.device, dtype=torch.float32) + 0.5) / sdt_probs.shape[1]\n", "#                 bin_centers = bin_centers.view(1, -1, 1, 1)\n", "#                 sdt = torch.sum(sdt_probs * bin_centers, dim=1)[0].cpu().numpy()\n", "                \n", "#                 plt.figure(figsize=(15, 5))\n", "#                 plt.subplot(1, 3, 1); plt.imshow(images[0, 0].cpu(), cmap='gray'); plt.title('Image')\n", "#                 plt.subplot(1, 3, 2); plt.imshow(semantic, cmap='viridis'); plt.title('Semantic')\n", "#                 plt.subplot(1, 3, 3); plt.imshow(sdt, cmap='magma'); plt.title('SDT')\n", "#                 plt.suptitle(f'Epoch {epoch+1}')\n", "#                 plt.savefig(os.path.join(model_dir, f'progress_epoch_{epoch+1}.png'))\n", "#                 plt.close()\n", "#             except Exception as e:\n", "#                 print(f\"Visualization error: {e}\")\n", "\n", "#     # Plot loss curves\n", "#     plt.figure(figsize=(12, 5))\n", "#     plt.subplot(1, 2, 1)\n", "#     plt.plot(train_losses, label='Train')\n", "#     plt.plot(val_losses, label='Val')\n", "#     plt.xlabel('Epoch'); plt.ylabel('Loss'); plt.title('Overall Loss'); plt.legend(); plt.grid(True)\n", "#     plt.subplot(1, 2, 2)\n", "#     for k in train_component_losses:\n", "#         plt.plot(train_component_losses[k], label=f'Train {k}')\n", "#         plt.plot(val_component_losses[k], label=f'Val {k}', linestyle='--')\n", "#     plt.xlabel('Epoch'); plt.ylabel('Loss'); plt.title('Component Losses'); plt.legend(); plt.grid(True)\n", "#     plt.tight_layout()\n", "#     plt.savefig(os.path.join(model_dir, 'loss_curves.png'))\n", "#     plt.close()\n", "    \n", "#     return model, train_losses, val_losses\n"]}, {"cell_type": "code", "execution_count": 30, "id": "7699948a", "metadata": {}, "outputs": [], "source": ["# Set paths\n", "image_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/\"\n", "mask_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/\"\n", "image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))\n", "mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))"]}, {"cell_type": "code", "execution_count": 31, "id": "5486f802", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset split: 857 training, 215 validation samples\n", "Data loader verified: Batch shape - images torch.<PERSON><PERSON>([12, 1, 256, 256]), targets torch.<PERSON><PERSON>([12, 7, 256, 256])\n", "Data types: images torch.float32, targets torch.float32\n", "1. Checking output shapes...\n", "  ✅ PASS: sem_out has 4 dimensions (B,C,H,W)\n", "  ✅ PASS: sdt_out has 4 dimensions (B,C,H,W)\n", "  ✅ PASS: skeleton_out has 4 dimensions (B,C,H,W)\n", "  ✅ PASS: hm_out has 4 dimensions (B,C,H,W)\n", "  ✅ PASS: flow_out has 4 dimensions (B,C,H,W)\n", "  ✅ PASS: Semantic output has 1 channel\n", "  ✅ PASS: SDT output has 11 channels (10 bins + background)\n", "  ✅ PASS: Skeleton output has 1 channel\n", "  ✅ PASS: Centroid output has 1 channel\n", "  ✅ PASS: Flow output has 2 channels (y,x)\n", "\n", "2. Checking value ranges...\n", "  ✅ PASS: sem_out in [0,1] (min: 0.500, max: 0.500)\n", "  ❌ FAIL: skeleton_out in [0,1] (min: -0.000, max: 0.000)\n", "  ✅ PASS: hm_out in [0,1] (min: 0.500, max: 0.500)\n", "  ✅ PASS: Flow magnitude <= 1.5 (max: 0.000)\n", "\n", "3. Checking SDT properties...\n", "  ✅ PASS: Sample 0: SDT low at skeleton (mean: 0.500)\n", "  ❌ FAIL: Sample 0: SDT higher at boundary (bound: 0.500, non-bound: 0.500)\n", "  ✅ PASS: Sample 1: SDT low at skeleton (mean: 0.500)\n", "  ❌ FAIL: Sample 1: SDT higher at boundary (bound: 0.500, non-bound: 0.500)\n", "\n", "4. Checking centroid localization and smoothness...\n", "  ✅ PASS: Sample 0: Centroids within semantic (100.0%)\n", "  ✅ PASS: Sample 0: Centroid heatmap smooth (max grad: 0.000)\n", "  ✅ PASS: Sample 1: Centroids within semantic (100.0%)\n", "  ✅ PASS: Sample 1: Centroid heatmap smooth (max grad: 0.000)\n", "\n", "5. Checking simplified instance segmentation...\n", "  ✅ PASS: <PERSON>ple 0: Instance seg successful (65535)\n", "  ✅ PASS: <PERSON>ple 1: Instance seg successful (65535)\n", "\n", "6. Generating visualization...\n", "\n", "2. Checking value ranges...\n", "  ✅ PASS: sem_out in [0,1] (min: 0.500, max: 0.500)\n", "  ❌ FAIL: skeleton_out in [0,1] (min: -0.000, max: 0.000)\n", "  ✅ PASS: hm_out in [0,1] (min: 0.500, max: 0.500)\n", "  ✅ PASS: Flow magnitude <= 1.5 (max: 0.000)\n", "\n", "3. Checking SDT properties...\n", "  ✅ PASS: Sample 0: SDT low at skeleton (mean: 0.500)\n", "  ❌ FAIL: Sample 0: SDT higher at boundary (bound: 0.500, non-bound: 0.500)\n", "  ✅ PASS: Sample 1: SDT low at skeleton (mean: 0.500)\n", "  ❌ FAIL: Sample 1: SDT higher at boundary (bound: 0.500, non-bound: 0.500)\n", "\n", "4. Checking centroid localization and smoothness...\n", "  ✅ PASS: Sample 0: Centroids within semantic (100.0%)\n", "  ✅ PASS: Sample 0: Centroid heatmap smooth (max grad: 0.000)\n", "  ✅ PASS: Sample 1: Centroids within semantic (100.0%)\n", "  ✅ PASS: Sample 1: Centroid heatmap smooth (max grad: 0.000)\n", "\n", "5. Checking simplified instance segmentation...\n", "  ✅ PASS: <PERSON>ple 0: Instance seg successful (65535)\n", "  ✅ PASS: <PERSON>ple 1: Instance seg successful (65535)\n", "\n", "6. Generating visualization...\n", "\n", "============================================================\n", "MODEL INSPECTION SUMMARY\n", "============================================================\n", "Passed checks: 32/38\n", "Max flow magnitude: 0.000\n", "Average centroid accuracy: 100.0%\n", "Average centroid gradient: 0.000 (lower = smoother)\n", "Avg instances per sample: 65535.0\n", "\n", "⚠️  INSPECTION FAILED: Some checks did not pass. Please review.\n"]}], "source": ["# Example usage (uncomment to run):\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48).to(device)\n", "# # Load a trained model if available\n", "# # checkpoint = torch.load('path/to/best_model.pth')\n", "# # model.load_state_dict(checkpoint['model_state_dict'])\n", "# \n", "# # Create data loaders (replace with your actual paths)\n", "train_loader, val_loader = create_skeleton_aware_data_loaders_fixed(image_paths, mask_paths)\n", "# \n", "# # Run inspection# \n", "results = inspect_skeleton_aware_model(model, val_loader, device, num_batches=2)"]}, {"cell_type": "code", "execution_count": 32, "id": "1647afcc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 1072 images and 1072 masks\n", "Sample image path: /mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/0  Series001  Green--FLUO--FITC_tile_1.tif\n", "Sample mask path: /mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/0  Series001  Green--FLUO--FITC_tile_1.tif\n", "✓ Matching mask found for first image\n", "Model will be saved to: /mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/\n", "Using device: cuda\n", "Creating skeleton-aware data loaders...\n", "Dataset split: 857 training, 215 validation samples\n", "Data loader verified: Batch shape - images torch.<PERSON><PERSON>([12, 1, 256, 256]), targets torch.<PERSON><PERSON>([12, 7, 256, 256])\n", "Data types: images torch.float32, targets torch.float32\n", "Training samples: 857\n", "Validation samples: 215\n", "Initializing skeleton-aware model...\n", "Model parameters: 1,605,474\n", "Starting SOTA skeleton-aware training (with resume and early stopping)...\n", "Model outputs will be saved to: /mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/\n", "Using loss function: SkeletonAwareLoss\n", "✅ Using fused AdamW optimizer.\n", "Using LR Scheduler: CosineAnnealingWarmRestarts\n", "Using Automatic Mixed Precision (AMP).\n", "Model moved to device: cuda\n", "No checkpoint found at /mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/best_model.pth. Starting from scratch.\n", "\n", "--- Starting/Resuming Training from epoch 1 to 300 ---\n", "📚 Curriculum: SDT=0.000, Flow=0.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 1/300 [Train]: 100%|██████████| 71/71 [01:51<00:00,  1.57s/it, Loss=1.9532, LR=4.22e-04, Grad_Norm=0.08]\n", "                                                                               \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 1 Summary:\n", "  Train Loss: 2.068668 | Val Loss: 1.985900 | LR: 4.22e-04\n", "  Train Components: semantic: 0.9776 | sdt: 0.0000 | skeleton: 0.0328 | centroid: 1.0419 | flow: 0.0000\n", "  Val Components:   semantic: 0.9515 | sdt: 0.0000 | skeleton: 0.0055 | centroid: 1.0262 | flow: 0.0000\n", "  ✅ New best model saved (Val Loss: 1.985900)\n", "  📊 Progress plot saved to /mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/progress_epoch_1.png\n", "📚 Curriculum: SDT=0.050, Flow=0.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 2/300 [Train]: 100%|██████████| 71/71 [01:36<00:00,  1.35s/it, Loss=1.9746, LR=9.76e-04, Grad_Norm=0.06]\n", "                                                                               \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 2 Summary:\n", "  Train Loss: 1.989641 | Val Loss: 1.969992 | LR: 9.76e-04\n", "  Train Components: semantic: 0.9526 | sdt: 0.1124 | skeleton: 0.0042 | centroid: 1.0252 | flow: 0.0000\n", "  Val Components:   semantic: 0.9410 | sdt: 0.0818 | skeleton: 0.0032 | centroid: 1.0201 | flow: 0.0000\n", "  ✅ New best model saved (Val Loss: 1.969992)\n", "📚 Curriculum: SDT=0.100, Flow=0.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 3/300 [Train]: 100%|██████████| 71/71 [01:36<00:00,  1.36s/it, Loss=1.9849, LR=2.74e-04, Grad_Norm=0.05]\n", "                                                                               \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 3 Summary:\n", "  Train Loss: 1.991759 | Val Loss: 1.974746 | LR: 2.74e-04\n", "  Train Components: semantic: 0.9486 | sdt: 0.1580 | skeleton: 0.0034 | centroid: 1.0223 | flow: 0.0000\n", "  Val Components:   semantic: 0.9386 | sdt: 0.1318 | skeleton: 0.0028 | centroid: 1.0187 | flow: 0.0000\n", "  ⏱️  Epochs since last improvement: 1\n", "📚 Curriculum: SDT=0.150, Flow=0.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 4/300 [Train]: 100%|██████████| 71/71 [01:32<00:00,  1.30s/it, Loss=1.9824, LR=9.05e-04, Grad_Norm=0.10]\n", "                                                                               \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 4 Summary:\n", "  Train Loss: 2.005204 | Val Loss: 1.987977 | LR: 9.05e-04\n", "  Train Components: semantic: 0.9473 | sdt: 0.2110 | skeleton: 0.0031 | centroid: 1.0216 | flow: 0.0000\n", "  Val Components:   semantic: 0.9384 | sdt: 0.1806 | skeleton: 0.0027 | centroid: 1.0184 | flow: 0.0000\n", "  ⏱️  Epochs since last improvement: 2\n", "📚 Curriculum: SDT=0.200, Flow=0.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 5/300 [Train]: 100%|██████████| 71/71 [01:31<00:00,  1.29s/it, Loss=2.0232, LR=1.47e-04, Grad_Norm=0.09]\n", "                                                                               \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 5 Summary:\n", "  Train Loss: 2.022437 | Val Loss: 2.002753 | LR: 1.47e-04\n", "  Train Components: semantic: 0.9445 | sdt: 0.2640 | skeleton: 0.0030 | centroid: 1.0206 | flow: 0.0000\n", "  Val Components:   semantic: 0.9378 | sdt: 0.2129 | skeleton: 0.0026 | centroid: 1.0185 | flow: 0.0000\n", "  ⏱️  Epochs since last improvement: 3\n", "📚 Curriculum: SDT=0.250, Flow=0.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 6/300 [Train]: 100%|██████████| 71/71 [01:31<00:00,  1.28s/it, Loss=2.0485, LR=7.94e-04, Grad_Norm=0.25]\n", "                                                                               \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 6 Summary:\n", "  Train Loss: 2.051579 | Val Loss: 2.034398 | LR: 7.94e-04\n", "  Train Components: semantic: 0.9455 | sdt: 0.3241 | skeleton: 0.0030 | centroid: 1.0206 | flow: 0.0000\n", "  Val Components:   semantic: 0.9374 | sdt: 0.2980 | skeleton: 0.0029 | centroid: 1.0181 | flow: 0.0000\n", "  ⏱️  Epochs since last improvement: 4\n", "📚 Curriculum: SDT=0.300, Flow=0.067, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 7/300 [Train]: 100%|██████████| 71/71 [01:30<00:00,  1.28s/it, Loss=2.0803, LR=5.54e-05, Grad_Norm=0.12]\n", "                                                                               \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 7 Summary:\n", "  Train Loss: 2.089757 | Val Loss: 2.053111 | LR: 5.54e-05\n", "  Train Components: semantic: 0.9460 | sdt: 0.3697 | skeleton: 0.0029 | centroid: 1.0208 | flow: 0.1143\n", "  Val Components:   semantic: 0.9366 | sdt: 0.2889 | skeleton: 0.0026 | centroid: 1.0179 | flow: 0.1197\n", "  ⏱️  Epochs since last improvement: 5\n", "📚 Curriculum: SDT=0.350, Flow=0.133, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 8/300 [Train]: 100%|██████████| 71/71 [01:32<00:00,  1.30s/it, Loss=2.1336, LR=6.55e-04, Grad_Norm=0.40]\n", "                                                                               \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 8 Summary:\n", "  Train Loss: 2.145419 | Val Loss: 2.104845 | LR: 6.55e-04\n", "  Train Components: semantic: 0.9455 | sdt: 0.4145 | skeleton: 0.0027 | centroid: 1.0206 | flow: 0.2258\n", "  Val Components:   semantic: 0.9371 | sdt: 0.3399 | skeleton: 0.0026 | centroid: 1.0181 | flow: 0.2008\n", "  ⏱️  Epochs since last improvement: 6\n", "📚 Curriculum: SDT=0.400, Flow=0.200, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 9/300 [Train]: 100%|██████████| 71/71 [01:29<00:00,  1.26s/it, Loss=2.2164, LR=7.15e-06, Grad_Norm=0.18]\n", "                                                                               \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 9 Summary:\n", "  Train Loss: 2.224557 | Val Loss: 2.137676 | LR: 7.15e-06\n", "  Train Components: semantic: 0.9455 | sdt: 0.4700 | skeleton: 0.0028 | centroid: 1.0206 | flow: 0.3315\n", "  Val Components:   semantic: 0.9368 | sdt: 0.3732 | skeleton: 0.0026 | centroid: 1.0180 | flow: 0.1488\n", "  ⏱️  Epochs since last improvement: 7\n", "📚 Curriculum: SDT=0.450, Flow=0.267, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 10/300 [Train]: 100%|██████████| 71/71 [01:32<00:00,  1.30s/it, Loss=2.2992, LR=5.01e-04, Grad_Norm=0.21]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 10 Summary:\n", "  Train Loss: 2.326433 | Val Loss: 2.202614 | LR: 5.01e-04\n", "  Train Components: semantic: 0.9454 | sdt: 0.5238 | skeleton: 0.0028 | centroid: 1.0206 | flow: 0.4523\n", "  Val Components:   semantic: 0.9369 | sdt: 0.4165 | skeleton: 0.0025 | centroid: 1.0180 | flow: 0.2121\n", "  ⏱️  Epochs since last improvement: 8\n", "📚 Curriculum: SDT=0.500, Flow=0.333, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 11/300 [Train]: 100%|██████████| 71/71 [01:31<00:00,  1.28s/it, Loss=2.3940, LR=9.94e-04, Grad_Norm=0.27]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 11 Summary:\n", "  Train Loss: 2.453500 | Val Loss: 2.373324 | LR: 9.94e-04\n", "  Train Components: semantic: 0.9464 | sdt: 0.5833 | skeleton: 0.0027 | centroid: 1.0208 | flow: 0.5717\n", "  Val Components:   semantic: 0.9359 | sdt: 0.4663 | skeleton: 0.0025 | centroid: 1.0180 | flow: 0.5478\n", "  ⏱️  Epochs since last improvement: 9\n", "📚 Curriculum: SDT=0.550, Flow=0.400, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 12/300 [Train]: 100%|██████████| 71/71 [01:33<00:00,  1.31s/it, Loss=2.5583, LR=3.46e-04, Grad_Norm=0.43]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 12 Summary:\n", "  Train Loss: 2.587283 | Val Loss: 2.527019 | LR: 3.46e-04\n", "  Train Components: semantic: 0.9461 | sdt: 0.6215 | skeleton: 0.0027 | centroid: 1.0207 | flow: 0.6868\n", "  Val Components:   semantic: 0.9371 | sdt: 0.5121 | skeleton: 0.0024 | centroid: 1.0178 | flow: 0.7170\n", "  ⏱️  Epochs since last improvement: 10\n", "📚 Curriculum: SDT=0.600, Flow=0.467, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 13/300 [Train]: 100%|██████████| 71/71 [01:32<00:00,  1.30s/it, Loss=2.7847, LR=9.46e-04, Grad_Norm=0.93]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 13 Summary:\n", "  Train Loss: 2.723854 | Val Loss: 2.625304 | LR: 9.46e-04\n", "  Train Components: semantic: 0.9465 | sdt: 0.6696 | skeleton: 0.0026 | centroid: 1.0208 | flow: 0.7518\n", "  Val Components:   semantic: 0.9373 | sdt: 0.5413 | skeleton: 0.0026 | centroid: 1.0178 | flow: 0.7319\n", "  ⏱️  Epochs since last improvement: 11\n", "📚 Curriculum: SDT=0.650, Flow=0.533, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 14/300 [Train]: 100%|██████████| 71/71 [01:30<00:00,  1.27s/it, Loss=2.8210, LR=2.07e-04, Grad_Norm=1.35]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 14 Summary:\n", "  Train Loss: 2.922477 | Val Loss: 2.554426 | LR: 2.07e-04\n", "  Train Components: semantic: 0.9453 | sdt: 0.7323 | skeleton: 0.0028 | centroid: 1.0204 | flow: 0.8936\n", "  Val Components:   semantic: 0.9370 | sdt: 0.6329 | skeleton: 0.0028 | centroid: 1.0178 | flow: 0.3450\n", "  ⏱️  Epochs since last improvement: 12\n", "📚 Curriculum: SDT=0.700, Flow=0.600, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 15/300 [Train]: 100%|██████████| 71/71 [01:32<00:00,  1.31s/it, Loss=3.2250, LR=8.54e-04, Grad_Norm=2.37]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 15 Summary:\n", "  Train Loss: 3.147899 | Val Loss: 2.982045 | LR: 8.54e-04\n", "  Train Components: semantic: 0.9479 | sdt: 0.8208 | skeleton: 0.0029 | centroid: 1.0211 | flow: 1.0001\n", "  Val Components:   semantic: 0.9372 | sdt: 0.8077 | skeleton: 0.0028 | centroid: 1.0182 | flow: 0.7617\n", "  ⏱️  Epochs since last improvement: 13\n", "📚 Curriculum: SDT=0.750, Flow=0.667, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 16/300 [Train]: 100%|██████████| 71/71 [01:31<00:00,  1.29s/it, Loss=3.2998, LR=9.64e-05, Grad_Norm=0.68]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 16 Summary:\n", "  Train Loss: 3.375135 | Val Loss: 3.142438 | LR: 9.64e-05\n", "  Train Components: semantic: 0.9461 | sdt: 0.8597 | skeleton: 0.0027 | centroid: 1.0209 | flow: 1.1388\n", "  Val Components:   semantic: 0.9380 | sdt: 0.6938 | skeleton: 0.0027 | centroid: 1.0185 | flow: 0.9925\n", "  ⏱️  Epochs since last improvement: 14\n", "  📊 Progress plot saved to /mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/progress_epoch_16.png\n", "📚 Curriculum: SDT=0.800, Flow=0.733, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 17/300 [Train]: 100%|██████████| 71/71 [01:33<00:00,  1.32s/it, Loss=3.6054, LR=7.27e-04, Grad_Norm=1.68]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 17 Summary:\n", "  Train Loss: 3.644851 | Val Loss: 3.610435 | LR: 7.27e-04\n", "  Train Components: semantic: 0.9466 | sdt: 0.9316 | skeleton: 0.0030 | centroid: 1.0212 | flow: 1.2645\n", "  Val Components:   semantic: 0.9377 | sdt: 0.7298 | skeleton: 0.0024 | centroid: 1.0186 | flow: 1.4546\n", "  ⏱️  Epochs since last improvement: 15\n", "📚 Curriculum: SDT=0.850, Flow=0.800, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 18/300 [Train]: 100%|██████████| 71/71 [01:32<00:00,  1.30s/it, Loss=3.7818, LR=2.54e-05, Grad_Norm=1.73]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 18 Summary:\n", "  Train Loss: 3.892060 | Val Loss: 3.556282 | LR: 2.54e-05\n", "  Train Components: semantic: 0.9448 | sdt: 0.9828 | skeleton: 0.0028 | centroid: 1.0208 | flow: 1.3586\n", "  Val Components:   semantic: 0.9384 | sdt: 0.7440 | skeleton: 0.0025 | centroid: 1.0189 | flow: 1.2036\n", "  ⏱️  Epochs since last improvement: 16\n", "📚 Curriculum: SDT=0.900, Flow=0.867, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 19/300 [Train]: 100%|██████████| 71/71 [01:33<00:00,  1.31s/it, Loss=4.4514, LR=5.79e-04, Grad_Norm=2.24]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 19 Summary:\n", "  Train Loss: 4.190869 | Val Loss: 3.427116 | LR: 5.79e-04\n", "  Train Components: semantic: 0.9464 | sdt: 0.9940 | skeleton: 0.0027 | centroid: 1.0211 | flow: 1.5285\n", "  Val Components:   semantic: 0.9379 | sdt: 1.0280 | skeleton: 0.0029 | centroid: 1.0191 | flow: 0.6238\n", "  ⏱️  Epochs since last improvement: 17\n", "📚 Curriculum: SDT=0.950, Flow=0.933, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 20/300 [Train]: 100%|██████████| 71/71 [01:31<00:00,  1.28s/it, Loss=4.6049, LR=1.00e-03, Grad_Norm=1.84]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 20 Summary:\n", "  Train Loss: 4.488835 | Val Loss: 4.905658 | LR: 1.00e-03\n", "  Train Components: semantic: 0.9474 | sdt: 1.0934 | skeleton: 0.0028 | centroid: 1.0216 | flow: 1.5824\n", "  Val Components:   semantic: 0.9379 | sdt: 0.8368 | skeleton: 0.0025 | centroid: 1.0188 | flow: 2.3038\n", "  ⏱️  Epochs since last improvement: 18\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 21/300 [Train]: 100%|██████████| 71/71 [01:32<00:00,  1.30s/it, Loss=4.7334, LR=4.22e-04, Grad_Norm=1.79]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 21 Summary:\n", "  Train Loss: 4.702201 | Val Loss: 4.046641 | LR: 4.22e-04\n", "  Train Components: semantic: 0.9472 | sdt: 1.0980 | skeleton: 0.0026 | centroid: 1.0216 | flow: 1.6314\n", "  Val Components:   semantic: 0.9375 | sdt: 0.8689 | skeleton: 0.0025 | centroid: 1.0188 | flow: 1.2177\n", "  ⏱️  Epochs since last improvement: 19\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 22/300 [Train]: 100%|██████████| 71/71 [01:30<00:00,  1.27s/it, Loss=4.5909, LR=9.76e-04, Grad_Norm=2.97]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 22 Summary:\n", "  Train Loss: 4.679468 | Val Loss: 4.507755 | LR: 9.76e-04\n", "  Train Components: semantic: 0.9488 | sdt: 1.1043 | skeleton: 0.0026 | centroid: 1.0220 | flow: 1.6004\n", "  Val Components:   semantic: 0.9401 | sdt: 0.9469 | skeleton: 0.0024 | centroid: 1.0192 | flow: 1.5980\n", "  ⏱️  Epochs since last improvement: 20\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 23/300 [Train]: 100%|██████████| 71/71 [01:32<00:00,  1.31s/it, Loss=4.6792, LR=2.74e-04, Grad_Norm=1.96]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 23 Summary:\n", "  Train Loss: 4.604999 | Val Loss: 4.808679 | LR: 2.74e-04\n", "  Train Components: semantic: 0.9501 | sdt: 1.0941 | skeleton: 0.0026 | centroid: 1.0228 | flow: 1.5342\n", "  Val Components:   semantic: 0.9411 | sdt: 0.8824 | skeleton: 0.0023 | centroid: 1.0202 | flow: 1.9615\n", "  ⏱️  Epochs since last improvement: 21\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 24/300 [Train]: 100%|██████████| 71/71 [01:32<00:00,  1.31s/it, Loss=4.4157, LR=9.05e-04, Grad_Norm=3.27]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 24 Summary:\n", "  Train Loss: 4.482374 | Val Loss: 4.728093 | LR: 9.05e-04\n", "  Train Components: semantic: 0.9494 | sdt: 1.0687 | skeleton: 0.0026 | centroid: 1.0226 | flow: 1.4378\n", "  Val Components:   semantic: 0.9418 | sdt: 0.9106 | skeleton: 0.0026 | centroid: 1.0206 | flow: 1.8513\n", "  ⏱️  Epochs since last improvement: 22\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 25/300 [Train]: 100%|██████████| 71/71 [01:33<00:00,  1.31s/it, Loss=4.3649, LR=1.47e-04, Grad_Norm=1.59]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 25 Summary:\n", "  Train Loss: 4.467219 | Val Loss: 4.438281 | LR: 1.47e-04\n", "  Train Components: semantic: 0.9501 | sdt: 1.0892 | skeleton: 0.0027 | centroid: 1.0230 | flow: 1.4009\n", "  Val Components:   semantic: 0.9403 | sdt: 0.8376 | skeleton: 0.0025 | centroid: 1.0205 | flow: 1.6361\n", "  ⏱️  Epochs since last improvement: 23\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 26/300 [Train]: 100%|██████████| 71/71 [01:34<00:00,  1.33s/it, Loss=4.4806, LR=7.94e-04, Grad_Norm=2.83]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 26 Summary:\n", "  Train Loss: 4.474972 | Val Loss: 3.736638 | LR: 7.94e-04\n", "  Train Components: semantic: 0.9495 | sdt: 1.0762 | skeleton: 0.0027 | centroid: 1.0232 | flow: 1.4221\n", "  Val Components:   semantic: 0.9416 | sdt: 0.8484 | skeleton: 0.0024 | centroid: 1.0208 | flow: 0.9223\n", "  ⏱️  Epochs since last improvement: 24\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 27/300 [Train]: 100%|██████████| 71/71 [01:34<00:00,  1.34s/it, Loss=4.2962, LR=5.54e-05, Grad_Norm=2.22]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 27 Summary:\n", "  Train Loss: 4.452699 | Val Loss: 4.834298 | LR: 5.54e-05\n", "  Train Components: semantic: 0.9492 | sdt: 1.0624 | skeleton: 0.0026 | centroid: 1.0232 | flow: 1.4140\n", "  Val Components:   semantic: 0.9401 | sdt: 0.8381 | skeleton: 0.0025 | centroid: 1.0206 | flow: 2.0317\n", "  ⏱️  Epochs since last improvement: 25\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 28/300 [Train]: 100%|██████████| 71/71 [01:33<00:00,  1.32s/it, Loss=4.5451, LR=6.55e-04, Grad_Norm=1.96]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 28 Summary:\n", "  Train Loss: 4.468965 | Val Loss: 4.137387 | LR: 6.55e-04\n", "  Train Components: semantic: 0.9495 | sdt: 1.0497 | skeleton: 0.0026 | centroid: 1.0237 | flow: 1.4422\n", "  Val Components:   semantic: 0.9415 | sdt: 0.8051 | skeleton: 0.0025 | centroid: 1.0215 | flow: 1.3656\n", "  ⏱️  Epochs since last improvement: 26\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 29/300 [Train]: 100%|██████████| 71/71 [01:34<00:00,  1.33s/it, Loss=4.1753, LR=7.15e-06, Grad_Norm=2.51]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 29 Summary:\n", "  Train Loss: 4.434594 | Val Loss: 4.952795 | LR: 7.15e-06\n", "  Train Components: semantic: 0.9484 | sdt: 1.0472 | skeleton: 0.0026 | centroid: 1.0232 | flow: 1.4119\n", "  Val Components:   semantic: 0.9409 | sdt: 0.7882 | skeleton: 0.0024 | centroid: 1.0207 | flow: 2.1995\n", "  ⏱️  Epochs since last improvement: 27\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 30/300 [Train]: 100%|██████████| 71/71 [01:34<00:00,  1.33s/it, Loss=4.4677, LR=5.01e-04, Grad_Norm=1.72]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 30 Summary:\n", "  Train Loss: 4.452093 | Val Loss: 4.085734 | LR: 5.01e-04\n", "  Train Components: semantic: 0.9489 | sdt: 1.0423 | skeleton: 0.0026 | centroid: 1.0233 | flow: 1.4337\n", "  Val Components:   semantic: 0.9410 | sdt: 0.8185 | skeleton: 0.0023 | centroid: 1.0211 | flow: 1.3017\n", "  ⏱️  Epochs since last improvement: 28\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 31/300 [Train]: 100%|██████████| 71/71 [01:35<00:00,  1.34s/it, Loss=4.4128, LR=9.94e-04, Grad_Norm=2.33]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 31 Summary:\n", "  Train Loss: 4.424592 | Val Loss: 4.398747 | LR: 9.94e-04\n", "  Train Components: semantic: 0.9504 | sdt: 1.0636 | skeleton: 0.0025 | centroid: 1.0234 | flow: 1.3833\n", "  Val Components:   semantic: 0.9404 | sdt: 0.7732 | skeleton: 0.0023 | centroid: 1.0207 | flow: 1.6609\n", "  ⏱️  Epochs since last improvement: 29\n", "  📊 Progress plot saved to /mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/progress_epoch_31.png\n", "📚 Curriculum: SDT=1.000, Flow=1.000, Cent=1.000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Epoch 32/300 [Train]: 100%|██████████| 71/71 [01:39<00:00,  1.40s/it, Loss=4.3272, LR=3.46e-04, Grad_Norm=2.52]\n", "                                                                                \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 32 Summary:\n", "  Train Loss: 4.423892 | Val Loss: 4.592680 | LR: 3.46e-04\n", "  Train Components: semantic: 0.9499 | sdt: 1.0343 | skeleton: 0.0025 | centroid: 1.0237 | flow: 1.4123\n", "  Val Components:   semantic: 0.9400 | sdt: 0.7880 | skeleton: 0.0024 | centroid: 1.0208 | flow: 1.8403\n", "  ⏱️  Epochs since last improvement: 30\n", "\n", "⚠️ Early stopping triggered after 32 epochs (patience 30).\n", "\n", "--- Training Completed ---\n", "💾 Final model checkpoint saved to /mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/final_model.pth\n", "📊 Final loss curves plot saved to /mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/final_loss_curves.png\n", "\n", "🎉 Training finished. Best validation loss: 1.969992\n", "Additional final model saved to: /mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/final_model_from_pipeline.pth\n", "Running skeleton-aware evaluation...\n", "Evaluation Results:\n", "  Precision: 0.0521 ± 0.0278\n", "  Recall: 0.9997 ± 0.0012\n", "  F1: 0.0978 ± 0.0498\n", "  Skeleton_iou: 0.4032 ± 0.1787\n", "  Sdt_mse: 0.1476 ± 0.0242\n", "  Boundary_iou: 0.0330 ± 0.0165\n", "Testing skeleton-aware inference...\n", "Testing on image 1: 0  Series001  Green--FLUO--FITC_tile_1.tif\n", "  ❌ Error during inference for 0  Series001  Green--FLUO--FITC_tile_1.tif: name 'tile_inference' is not defined\n", "  ❌ Error during visualization for 0  Series001  Green--FLUO--FITC_tile_1.tif: name 'inference_results' is not defined\n", "Testing on image 2: 0  Series001  Green--FLUO--FITC_tile_2.tif\n", "  ❌ Error during inference for 0  Series001  Green--FLUO--FITC_tile_2.tif: name 'tile_inference' is not defined\n", "  ❌ Error during visualization for 0  Series001  Green--FLUO--FITC_tile_2.tif: name 'inference_results' is not defined\n", "Testing on image 3: 0  Series001  Green--FLUO--FITC_tile_3.tif\n", "  ❌ Error during inference for 0  Series001  Green--FLUO--FITC_tile_3.tif: name 'tile_inference' is not defined\n", "  ❌ Error during visualization for 0  Series001  Green--FLUO--FITC_tile_3.tif: name 'inference_results' is not defined\n", "\n", "🎉 Training and inference pipeline completed successfully!\n", "🏁 All outputs and models are saved in: /mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/\n"]}], "source": ["# Cell 9: Complete Pipeline with Updated Paths and SOTA Components\n", "\n", "import os\n", "import glob\n", "import torch\n", "import numpy as np\n", "import tifffile\n", "import matplotlib.pyplot as plt\n", "from skimage.color import label2rgb\n", "\n", "# --- 1. <PERSON>s ---\n", "image_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/\"\n", "mask_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/\"\n", "image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))\n", "mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))\n", "\n", "print(f\"Found {len(image_paths)} images and {len(mask_paths)} masks\")\n", "\n", "# Verify a few paths\n", "if image_paths and mask_paths:\n", "    print(\"Sample image path:\", image_paths[0])\n", "    print(\"Sample mask path:\", mask_paths[0])\n", "    \n", "    # Check if corresponding files exist\n", "    base_name = os.path.splitext(os.path.basename(image_paths[0]))[0]\n", "    expected_mask = os.path.join(mask_dir, base_name + '.tif')\n", "    if os.path.exists(expected_mask):\n", "        print(\"✓ Matching mask found for first image\")\n", "    else:\n", "        print(\"⚠️  Matching mask not found for first image\")\n", "\n", "# --- 2. Configuration ---\n", "model_dir = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota_centroid/' # Updated directory name\n", "os.makedirs(model_dir, exist_ok=True)\n", "print(f\"Model will be saved to: {model_dir}\")\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "# --- 3. Create Data Loaders (using the FIXED version) ---\n", "print(\"Creating skeleton-aware data loaders...\")\n", "# Disable pin_memory to prevent CUDA OOM errors as identified previously\n", "train_loader, val_loader = create_skeleton_aware_data_loaders_fixed(\n", "    image_paths, mask_paths, \n", "    batch_size=12, \n", "    patch_size=256, \n", "    num_workers=8 # Reduced from 4 to potentially improve stability if stuttering was an issue\n", ")\n", "\n", "print(f\"Training samples: {len(train_loader.dataset)}\")\n", "print(f\"Validation samples: {len(val_loader.dataset)}\")\n", "\n", "# --- 4. Initialize Model ---\n", "print(\"Initializing skeleton-aware model...\")\n", "# Use the latest, corrected model definition\n", "model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48) \n", "print(f\"Model parameters: {sum(p.numel() for p in model.parameters()):,}\")\n", "\n", "# --- 5. TRAINING with SOTA Function ---\n", "print(\"Starting SOTA skeleton-aware training (with resume and early stopping)...\")\n", "# Use the State-of-the-Art training function with early stopping\n", "trained_model, train_losses, val_losses = train_skeleton_aware_model_sota(\n", "    model, \n", "    train_loader, \n", "    val_loader, \n", "    num_epochs=300,\n", "    device=device,\n", "    model_dir=model_dir,\n", "    resume=True, # Set to False if you want to force a fresh start\n", "    early_stopping_patience=30 # Stop if no improvement for 30 epochs\n", ")\n", "\n", "# --- 6. Save Final Model State ---\n", "# The SOTA function already saves 'best_model.pth' and 'final_model.pth'\n", "# This additional save is redundant but kept for explicitness if needed for a specific purpose.\n", "# It saves the model returned by the training function.\n", "final_model_path_additional = os.path.join(model_dir, 'final_model_from_pipeline.pth')\n", "torch.save({\n", "    'model_state_dict': trained_model.state_dict(),\n", "    'model_config': {\n", "        'in_ch': 1,\n", "        'base_ch': 48\n", "    },\n", "    'train_losses': train_losses,\n", "    'val_losses': val_losses\n", "}, final_model_path_additional)\n", "print(f\"Additional final model saved to: {final_model_path_additional}\")\n", "\n", "# --- 7. <PERSON><PERSON><PERSON> ---\n", "print(\"Running skeleton-aware evaluation...\")\n", "\n", "\n", "def evaluate_skeleton_aware_model(model, val_loader, device='cuda'):\n", "    model.eval()\n", "    all_metrics = {'precision': [], 'recall': [], 'f1': [], 'skeleton_iou': [], 'sdt_mse': [], 'boundary_iou': []}\n", "    with torch.no_grad():\n", "        for images, targets in val_loader:\n", "            images = images.to(device, non_blocking=True)\n", "            targets = targets.to(device, non_blocking=True)\n", "            batch_size = images.shape[0]\n", "            for i in range(batch_size):\n", "                try:\n", "                    with autocast('cuda'):\n", "                        outputs = model(images[i:i+1])\n", "                    # Process SDT\n", "                    sdt_probs = F.softmax(outputs['sdt_out'], dim=1)\n", "                    bin_centers = (torch.arange(0, sdt_probs.shape[1], device=sdt_probs.device, dtype=torch.float32) + 0.5) / sdt_probs.shape[1]\n", "                    bin_centers = bin_centers.view(1, -1, 1, 1)\n", "                    pred_sdt = torch.sum(sdt_probs * bin_centers, dim=1, keepdim=True)\n", "                    # Predictions\n", "                    pred_semantic = torch.sigmoid(outputs['sem_out']) > 0.5\n", "                    pred_skeleton = torch.sigmoid(outputs['skeleton_out']) > 0.5\n", "                    # Ground truth\n", "                    gt_semantic = targets[i:i+1, 0:1] > 0.5\n", "                    gt_skeleton = targets[i:i+1, 2:3] > 0.5\n", "                    gt_boundary = targets[i:i+1, 6:7] > 0.5 # Corrected index\n", "                    # Metrics\n", "                    tp = (pred_semantic & gt_semantic).float().sum().item()\n", "                    fp = (pred_semantic & ~gt_semantic).float().sum().item()\n", "                    fn = (~pred_semantic & gt_semantic).float().sum().item()\n", "                    precision = tp / (tp + fp + 1e-6)\n", "                    recall = tp / (tp + fn + 1e-6)\n", "                    f1 = 2 * (precision * recall) / (precision + recall + 1e-6)\n", "                    skeleton_iou = (pred_skeleton & gt_skeleton).float().sum().item() / ((pred_skeleton | gt_skeleton).float().sum().item() + 1e-6)\n", "                    boundary_iou = (pred_semantic & gt_boundary).float().sum().item() / ((pred_semantic | gt_boundary).float().sum().item() + 1e-6)\n", "                    sdt_mse = F.mse_loss(pred_sdt, targets[i:i+1, 1:2]).item()\n", "                    \n", "                    all_metrics['precision'].append(precision)\n", "                    all_metrics['recall'].append(recall)\n", "                    all_metrics['f1'].append(f1)\n", "                    all_metrics['skeleton_iou'].append(skeleton_iou)\n", "                    all_metrics['sdt_mse'].append(sdt_mse)\n", "                    all_metrics['boundary_iou'].append(boundary_iou)\n", "                except Exception as e:\n", "                    print(f\"Evaluation error: {e}\")\n", "                    continue\n", "    eval_metrics = {}\n", "    for k, v in all_metrics.items():\n", "        eval_metrics[k] = np.mean(v) if v else 0.0\n", "        eval_metrics[f'std_{k}'] = np.std(v) if v else 0.0\n", "    return eval_metrics\n", "\n", "\n", "# Ensure evaluate_skeleton_aware_model is defined or imported\n", "eval_metrics = evaluate_skeleton_aware_model(trained_model, val_loader, device)\n", "print(f\"Evaluation Results:\")\n", "for k, v in eval_metrics.items():\n", "    if not k.startswith('std_'):\n", "        std_key = f'std_{k}'\n", "        std_val = eval_metrics.get(std_key, 'N/A')\n", "        print(f\"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}\")\n", "\n", "# --- 8. Test Inference on Sample Images ---\n", "print(\"Testing skeleton-aware inference...\")\n", "test_images = image_paths[:3]  # Test on first 3 images\n", "\n", "# --- CRITICAL: Use the FIXED inference function ---\n", "# This should be the `tile_inference` function that correctly handles SDT reconstruction\n", "# and uses the improved instance segmentation.\n", "# Assuming `tile_inference` is defined in the current notebook/script context.\n", "# If it's in a separate file, import it: from your_inference_module import tile_inference\n", "\n", "for i, test_path in enumerate(test_images):\n", "    print(f\"Testing on image {i+1}: {os.path.basename(test_path)}\")\n", "    \n", "    # --- Use the robust tiled inference function ---\n", "    # This function handles large images, SDT correctly, and instance segmentation\n", "    try:\n", "        inference_results = tile_inference(\n", "            trained_model,\n", "            test_path,\n", "            device=device,\n", "            patch_size=256,\n", "            overlap=0.5, # Standard overlap\n", "            batch_size=1 # Process one patch at a time for stability during testing\n", "        )\n", "        # Extract results\n", "        # The keys depend on the exact output of your `tile_inference` function\n", "        # Based on the provided `tile_inference` code:\n", "        semantic_result = inference_results.get('semantic', np.zeros((10, 10)))\n", "        sdt_result = inference_results.get('sdt_scalar', np.zeros((10, 10))) # Use scalar SDT\n", "        skeleton_result = inference_results.get('skeleton', np.zeros((10, 10)))\n", "        instance_labels_result = inference_results.get('instance_labels', np.zeros((10, 10), dtype=int))\n", "        spots_result = inference_results.get('spots', [])\n", "        \n", "        # Load original image for visualization\n", "        original_image = tifffile.imread(test_path).astype(np.float32)\n", "        vmin, vmax = np.percentile(original_image, (1, 99))\n", "        original_image_norm = np.clip((original_image - vmin) / (vmax - vmin + 1e-8), 0, 1)\n", "\n", "    except Exception as e:\n", "        print(f\"  ❌ Error during inference for {os.path.basename(test_path)}: {e}\")\n", "        # Create dummy results to allow visualization code to run\n", "        original_image_norm = np.zeros((256, 256))\n", "        semantic_result = np.zeros((256, 256))\n", "        sdt_result = np.zeros((256, 256))\n", "        skeleton_result = np.zeros((256, 256))\n", "        instance_labels_result = np.zeros((256, 256), dtype=int)\n", "        spots_result = []\n", "\n", "    # --- Visualize results ---\n", "    try:\n", "        plt.figure(figsize=(20, 15)) # Increased figure size for more plots\n", "        \n", "        # 1. Original image with detected spots\n", "        plt.subplot(3, 3, 1)\n", "        plt.imshow(original_image_norm, cmap='gray')\n", "        if spots_result:\n", "            # Handle both list of dicts and array formats\n", "            if isinstance(spots_result, list) and len(spots_result) > 0 and isinstance(spots_result[0], dict):\n", "                 spots_y = [spot['y'] for spot in spots_result]\n", "                 spots_x = [spot['x'] for spot in spots_result]\n", "            elif isinstance(spots_result, np.ndarray) and spots_result.size > 0:\n", "                 spots_y = spots_result[:, 0]\n", "                 spots_x = spots_result[:, 1]\n", "            else:\n", "                 spots_y, spots_x = [], []\n", "            if spots_y:\n", "                plt.scatter(spots_x, spots_y, c='red', s=30, marker='o', alpha=0.7)\n", "        plt.title(f\"Detected Spots: {len(spots_result)}\")\n", "        plt.axis('off')\n", "\n", "        # 2. Semantic mask\n", "        plt.subplot(3, 3, 2)\n", "        plt.imshow(semantic_result, cmap='viridis')\n", "        plt.title(\"Semantic Mask\")\n", "        plt.axis('off')\n", "        plt.colorbar(shrink=0.8)\n", "\n", "        # 3. <PERSON><PERSON> (Scalar)\n", "        plt.subplot(3, 3, 3)\n", "        plt.imshow(sdt_result, cmap='magma')\n", "        plt.title(\"SDT (Scalar)\")\n", "        plt.axis('off')\n", "        plt.colorbar(shrink=0.8)\n", "\n", "        # 4. <PERSON><PERSON><PERSON>\n", "        plt.subplot(3, 3, 4)\n", "        plt.imshow(skeleton_result, cmap='bone')\n", "        plt.title(\"Skeleton\")\n", "        plt.axis('off')\n", "\n", "        # 5. Instance segmentation\n", "        plt.subplot(3, 3, 5)\n", "        instance_viz = label2rgb(instance_labels_result, bg_label=0, alpha=0.7)\n", "        plt.imshow(instance_viz)\n", "        plt.title(f\"Instance Segmentation ({np.max(instance_labels_result)} instances)\")\n", "        plt.axis('off')\n", "\n", "        # 6. Centroid Map (if available in results)\n", "        centroid_result = inference_results.get('centroid', np.zeros((10, 10)))\n", "        plt.subplot(3, 3, 6)\n", "        plt.imshow(centroid_result, cmap='hot')\n", "        plt.title(\"Centroid Heatmap\")\n", "        plt.axis('off')\n", "        plt.colorbar(shrink=0.8)\n", "        \n", "        # 7. Flow Magnitude (if available)\n", "        flow_result = inference_results.get('flow', np.zeros((2, 10, 10)))\n", "        if flow_result is not None and flow_result.ndim == 3 and flow_result.shape[0] == 2:\n", "            flow_mag = np.sqrt(flow_result[0]**2 + flow_result[1]**2 + 1e-8)\n", "            plt.subplot(3, 3, 7)\n", "            plt.imshow(flow_mag, cmap='plasma')\n", "            plt.title(\"Flow Magnitude\")\n", "            plt.axis('off')\n", "            plt.colorbar(shrink=0.8)\n", "        else:\n", "             plt.subplot(3, 3, 7)\n", "             plt.text(0.5, 0.5, 'Flow Not Available', ha='center', va='center')\n", "             plt.title(\"Flow\")\n", "             plt.axis('off')\n", "\n", "        # 8. Overlay of instances on image\n", "        plt.subplot(3, 3, 8)\n", "        plt.imshow(original_image_norm, cmap='gray')\n", "        # Overlay instance boundaries\n", "        if np.max(instance_labels_result) > 0:\n", "            # Simple boundary visualization\n", "            from skimage.segmentation import find_boundaries\n", "            boundaries = find_boundaries(instance_labels_result, mode='outer')\n", "            plt.imshow(boundaries, cmap='red', alpha=0.5)\n", "        plt.title(\"Instances Overlay\")\n", "        plt.axis('off')\n", "        \n", "        # 9. Summary text or another relevant plot\n", "        plt.subplot(3, 3, 9)\n", "        plt.text(0.1, 0.8, f\"Image: {os.path.basename(test_path)}\", fontsize=10)\n", "        plt.text(0.1, 0.6, f\"Spots Detected: {len(spots_result)}\", fontsize=10)\n", "        plt.text(0.1, 0.4, f\"Max Instance ID: {np.max(instance_labels_result)}\", fontsize=10)\n", "        # Add a simple histogram of SDT values if interesting\n", "        if sdt_result is not None and sdt_result.size > 1:\n", "            plt.text(0.1, 0.2, f\"SDT Mean: {np.mean(sdt_result):.3f}\", fontsize=10)\n", "        plt.title(\"Summary\")\n", "        plt.axis('off')\n", "        plt.gca().set_facecolor('lightgray') # Background for text\n", "\n", "        plt.suptitle(f\"Inference Results for {os.path.basename(test_path)}\", fontsize=16)\n", "        plt.tight_layout()\n", "        result_plot_path = os.path.join(model_dir, f'inference_result_{i+1}_{os.path.splitext(os.path.basename(test_path))[0]}.png')\n", "        plt.savefig(result_plot_path, dpi=150, bbox_inches='tight')\n", "        plt.close()\n", "        print(f\"  📊 Inference results plot saved to: {result_plot_path}\")\n", "        print(f\"  🧮 Detected {len(spots_result)} spots/instances.\")\n", "        \n", "    except Exception as e:\n", "        print(f\"  ❌ Error during visualization for {os.path.basename(test_path)}: {e}\")\n", "        # Try to close any open figures to prevent resource leaks\n", "        plt.close('all') \n", "\n", "# --- 9. Memory cleanup ---\n", "torch.cuda.empty_cache()\n", "print(\"\\n🎉 Training and inference pipeline completed successfully!\")\n", "print(f\"🏁 All outputs and models are saved in: {model_dir}\")"]}, {"cell_type": "code", "execution_count": null, "id": "be3ce0cf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 33, "id": "6a91eb91", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Refinement code is ready. Uncomment the example usage section to run.\n"]}], "source": ["# Cell: Model Refinement and Further Improvement\n", "\n", "import torch\n", "import torch.nn as nn\n", "import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import precision_recall_curve, average_precision_score\n", "\n", "def refine_trained_model(\n", "    initial_model_path, \n", "    image_paths, \n", "    mask_paths,\n", "    refinement_config,\n", "    device='cuda'\n", "):\n", "    \"\"\"\n", "    Refine a trained model based on initial results.\n", "\n", "    Args:\n", "        initial_model_path (str): Path to the 'best_model.pth' or 'final_model.pth'.\n", "        image_paths (list): List of image file paths.\n", "        mask_paths (list): List of mask file paths.\n", "        refinement_config (dict): Configuration for refinement.\n", "            - 'strategy': 'fine_tune' or 'evaluate_only'\n", "            - 'new_lr': New learning rate for fine-tuning.\n", "            - 'new_epochs': Number of epochs for fine-tuning.\n", "            - 'batch_size': Batch size for refinement data loaders.\n", "            - 'patch_size': Patch size for refinement data loaders.\n", "            - 'num_workers': Number of workers for data loaders.\n", "            - 'loss_weights': Optional dict to adjust loss function weights.\n", "        device (str): Devi<PERSON> to run on ('cuda' or 'cpu').\n", "\n", "    Returns:\n", "        dict: Results of refinement including model path and metrics.\n", "    \"\"\"\n", "    print(\"--- Model Refinement Process ---\")\n", "    print(f\"Loading initial model from: {initial_model_path}\")\n", "    \n", "    # --- 1. <PERSON><PERSON> Initial Model ---\n", "    if not os.path.exists(initial_model_path):\n", "        raise FileNotFoundError(f\"Initial model path not found: {initial_model_path}\")\n", "        \n", "    checkpoint = torch.load(initial_model_path, map_location=device, weights_only=False)\n", "    \n", "    # Determine model configuration\n", "    # Assuming the model config is saved, otherwise use defaults\n", "    model_config = checkpoint.get('model_config', {'in_ch': 1, 'base_ch': 48})\n", "    print(f\"Model config: {model_config}\")\n", "    \n", "    # Initialize model\n", "    refined_model = SkeletonAwareSpotDetector(**model_config).to(device)\n", "    refined_model.load_state_dict(checkpoint['model_state_dict'])\n", "    print(\"✅ Initial model loaded successfully.\")\n", "\n", "    # --- 2. Prepare Data ---\n", "    print(\"\\n--- Preparing Data for Refinement ---\")\n", "    # Use the fixed data loader function\n", "    refinement_train_loader, refinement_val_loader = create_skeleton_aware_data_loaders_fixed(\n", "        image_paths, mask_paths,\n", "        batch_size=refinement_config.get('batch_size', 12),\n", "        patch_size=refinement_config.get('patch_size', 256),\n", "        num_workers=refinement_config.get('num_workers', 4)\n", "    )\n", "    print(f\"Refinement DataLoader - Train: {len(refinement_train_loader.dataset)}, Val: {len(refinement_val_loader.dataset)}\")\n", "\n", "    # --- 3. Refinement Strategy ---\n", "    strategy = refinement_config.get('strategy', 'evaluate_only')\n", "    \n", "    if strategy == 'evaluate_only':\n", "        print(\"\\n--- Evaluation Only Strategy ---\")\n", "        refined_model.eval()\n", "        print(\"Running detailed evaluation on validation set...\")\n", "        final_metrics = evaluate_skeleton_aware_model(refined_model, refinement_val_loader, device)\n", "        print(\"Final Refined Model Evaluation Metrics:\")\n", "        for k, v in final_metrics.items():\n", "            if not k.startswith('std_'):\n", "                std_key = f'std_{k}'\n", "                std_val = final_metrics.get(std_key, 'N/A')\n", "                print(f\"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}\")\n", "        \n", "        # Run inspection on the refined model\n", "        print(\"\\nRunning model inspection on refined model...\")\n", "        inspection_results = inspect_skeleton_aware_model(\n", "            refined_model, refinement_val_loader, device, num_batches=2,\n", "            save_dir=os.path.join(model_dir, \"refinement_inspection\")\n", "        )\n", "        \n", "        return {\n", "            'status': 'evaluation_completed',\n", "            'model_path': initial_model_path,\n", "            'metrics': final_metrics,\n", "            'inspection_results': inspection_results\n", "        }\n", "\n", "    elif strategy == 'fine_tune':\n", "        print(\"\\n--- Fine-Tuning Strategy ---\")\n", "        \n", "        # --- 4. <PERSON><PERSON>-Tuning ---\n", "        # Create a new model directory for refined model\n", "        refined_model_dir = os.path.join(model_dir, \"refined_model\")\n", "        os.makedirs(refined_model_dir, exist_ok=True)\n", "        print(f\"Refined model will be saved to: {refined_model_dir}\")\n", "        \n", "        # Adjust loss function if specified\n", "        if 'loss_weights' in refinement_config:\n", "            print(\"Adjusting loss function weights for fine-tuning...\")\n", "            # This would require modifying the loss function instance\n", "            # For simplicity here, we assume the main loss function is used\n", "            # A more robust approach would be to create a new loss instance\n", "            # with the specified weights.\n", "            print(\"Note: Loss weight adjustment not implemented in this snippet. Modify SkeletonAwareLoss directly if needed.\")\n", "        \n", "        # Freeze certain layers if specified (example)\n", "        if refinement_config.get('freeze_encoder', False):\n", "            print(\"Freezing encoder layers...\")\n", "            for name, param in refined_model.named_parameters():\n", "                if name.startswith(('stem_conv', 'enc1', 'enc2')):\n", "                    param.requires_grad = False\n", "            print(\"Encoder layers frozen.\")\n", "        \n", "        # Setup optimizer and scheduler for fine-tuning\n", "        new_lr = refinement_config.get('new_lr', 1e-4) # Lower LR for fine-tuning\n", "        new_epochs = refinement_config.get('new_epochs', 50)\n", "        print(f\"Fine-tuning with LR: {new_lr}, Epochs: {new_epochs}\")\n", "        \n", "        optimizer = torch.optim.AdamW(\n", "            filter(lambda p: p.requires_grad, refined_model.parameters()), # Only optimize unfrozen params\n", "            lr=new_lr,\n", "            weight_decay=1e-4,\n", "            betas=(0.9, 0.999)\n", "        )\n", "        scheduler = torch.optim.lr_scheduler.OneCycleLR(\n", "            optimizer,\n", "            max_lr=new_lr,\n", "            epochs=new_epochs,\n", "            steps_per_epoch=len(refinement_train_loader),\n", "            pct_start=0.1,\n", "            anneal_strategy='cos'\n", "        )\n", "        \n", "        # Use the main loss function\n", "        criterion = SkeletonAwareLoss() # Re-instantiate or use existing if accessible\n", "        \n", "        # --- 5. Fine-Tuning Loop (Simplified version of the main training loop) ---\n", "        print(\"Starting fine-tuning...\")\n", "        best_loss = float('inf')\n", "        refined_train_losses, refined_val_losses = [], []\n", "        \n", "        scaler = GradScaler() # Re-use AMP scaler\n", "        \n", "        for epoch in range(new_epochs):\n", "            # Training\n", "            refined_model.train()\n", "            epoch_train_losses = []\n", "            pbar = tqdm(refinement_train_loader, desc=f\"FT Epoch {epoch+1}/{new_epochs}\")\n", "            for images, targets in pbar:\n", "                images = images.float().to(device, non_blocking=True)\n", "                targets = targets.float().to(device, non_blocking=True)\n", "                optimizer.zero_grad(set_to_none=True)\n", "                with autocast('cuda'):\n", "                    outputs = refined_model(images)\n", "                    loss, _ = criterion(outputs, targets)\n", "                if not torch.isnan(loss):\n", "                    scaler.scale(loss).backward()\n", "                    scaler.unscale_(optimizer)\n", "                    torch.nn.utils.clip_grad_norm_(refined_model.parameters(), max_norm=1.0)\n", "                    scaler.step(optimizer)\n", "                    scaler.update()\n", "                    scheduler.step()\n", "                    epoch_train_losses.append(loss.item())\n", "                pbar.set_postfix({'Loss': f'{loss.item():.4f}' if not torch.isnan(loss) else 'NaN'})\n", "            \n", "            train_loss = np.mean(epoch_train_losses) if epoch_train_losses else float('inf')\n", "            refined_train_losses.append(train_loss)\n", "            \n", "            # Validation\n", "            refined_model.eval()\n", "            epoch_val_losses = []\n", "            with torch.no_grad():\n", "                for images, targets in refinement_val_loader:\n", "                    images = images.float().to(device, non_blocking=True)\n", "                    targets = targets.float().to(device, non_blocking=True)\n", "                    with autocast('cuda'):\n", "                        outputs = refined_model(images)\n", "                        loss, _ = criterion(outputs, targets)\n", "                    if not torch.isnan(loss):\n", "                        epoch_val_losses.append(loss.item())\n", "            \n", "            val_loss = np.mean(epoch_val_losses) if epoch_val_losses else float('inf')\n", "            refined_val_losses.append(val_loss)\n", "            \n", "            print(f\"FT Epoch {epoch+1}: Train Loss: {train_loss:.4f} | Val Loss: {val_loss:.4f}\")\n", "            print(f\"  LR: {optimizer.param_groups[0]['lr']:.6e}\")\n", "            \n", "            # Save best refined model\n", "            if val_loss < best_loss:\n", "                best_loss = val_loss\n", "                refined_checkpoint_path = os.path.join(refined_model_dir, 'best_refined_model.pth')\n", "                torch.save({\n", "                    'model_state_dict': refined_model.state_dict(),\n", "                    'optimizer_state_dict': optimizer.state_dict(),\n", "                    'scheduler_state_dict': scheduler.state_dict(),\n", "                    'epoch': epoch,\n", "                    'loss': best_loss,\n", "                    'model_config': model_config,\n", "                    'refinement_config': refinement_config\n", "                }, refined_checkpoint_path)\n", "                print(f\"✅ Best refined model saved (val_loss={best_loss:.4f})\")\n", "        \n", "        # Plot refinement loss curves\n", "        plt.figure(figsize=(10, 5))\n", "        plt.plot(refined_train_losses, label='Refined Train Loss')\n", "        plt.plot(refined_val_losses, label='Refined Val Loss')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Loss')\n", "        plt.title('Refinement Loss Curves')\n", "        plt.legend()\n", "        plt.grid(True)\n", "        plt.savefig(os.path.join(refined_model_dir, 'refinement_loss_curves.png'))\n", "        plt.close()\n", "        print(\"Refinement loss curves saved.\")\n", "        \n", "        # --- 6. Final Evaluation ---\n", "        print(\"\\nRunning final evaluation on refined model...\")\n", "        final_metrics = evaluate_skeleton_aware_model(refined_model, refinement_val_loader, device)\n", "        print(\"Final Refined Model Evaluation Metrics:\")\n", "        for k, v in final_metrics.items():\n", "            if not k.startswith('std_'):\n", "                std_key = f'std_{k}'\n", "                std_val = final_metrics.get(std_key, 'N/A')\n", "                print(f\"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}\")\n", "        \n", "        # Run inspection on the refined model\n", "        print(\"\\nRunning model inspection on refined model...\")\n", "        inspection_results = inspect_skeleton_aware_model(\n", "            refined_model, refinement_val_loader, device, num_batches=2,\n", "            save_dir=os.path.join(refined_model_dir, \"refinement_inspection\")\n", "        )\n", "        \n", "        return {\n", "            'status': 'fine_tuning_completed',\n", "            'model_path': refined_checkpoint_path,\n", "            'metrics': final_metrics,\n", "            'inspection_results': inspection_results,\n", "            'train_losses': refined_train_losses,\n", "            'val_losses': refined_val_losses\n", "        }\n", "        \n", "    else:\n", "        raise ValueError(f\"Unknown refinement strategy: {strategy}\")\n", "\n", "\n", "# --- Example Usage ---\n", "\n", "# 1. Specify paths (assuming these are already defined from your main script)\n", "# image_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/\"\n", "# mask_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/\"\n", "# image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))\n", "# mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))\n", "# model_dir = '/mnt/d/Users/<USER>/Documents/spot_detector_model/' # Main model directory\n", "\n", "# 2. Determine the path to the best model from initial training\n", "# This could be the 'best_model.pth' saved during training or 'final_model.pth'\n", "initial_model_path = os.path.join(model_dir, 'best_model.pth') # Or 'final_model.pth'\n", "\n", "# 3. Define refinement configurations\n", "\n", "# Option A: Evaluate Only (No further training)\n", "refinement_config_eval = {\n", "    'strategy': 'evaluate_only'\n", "}\n", "\n", "# Option B: Fine-Tune with lower LR and fewer epochs\n", "refinement_config_finetune = {\n", "    'strategy': 'fine_tune',\n", "    'new_lr': 5e-5,          # Lower learning rate for fine-tuning\n", "    'new_epochs': 30,        # Fewer epochs for refinement\n", "    'batch_size': 12,        # Can adjust if needed\n", "    'patch_size': 256,\n", "    'num_workers': 2,        # Reduce workers if you had stuttering issues\n", "    # 'loss_weights': {'w_sem': 1.2, 'w_sdt': 2.5} # Example of adjusting weights\n", "    # 'freeze_encoder': True   # Example of freezing encoder layers\n", "}\n", "\n", "# 4. Choose a configuration and run refinement\n", "# Uncomment one of the lines below to choose the strategy\n", "\n", "# For Evaluation Only:\n", "# refinement_results = refine_trained_model(\n", "#     initial_model_path, image_paths, mask_paths, refinement_config_eval, device\n", "# )\n", "\n", "# For Fine-Tuning:\n", "# refinement_results = refine_trained_model(\n", "#     initial_model_path, image_paths, mask_paths, refinement_config_finetune, device\n", "# )\n", "\n", "# 5. Access results\n", "# print(\"\\n--- Refinement Results ---\")\n", "# print(f\"Status: {refinement_results['status']}\")\n", "# print(f\"Model Path: {refinement_results['model_path']}\")\n", "# if 'metrics' in refinement_results:\n", "#     print(\"Final Metrics:\")\n", "#     for k, v in refinement_results['metrics'].items():\n", "#         if not k.startswith('std_'):\n", "#             std_key = f'std_{k}'\n", "#             std_val = refinement_results['metrics'].get(std_key, 'N/A')\n", "#             print(f\"  {k.capitalize()}: {v:.4f} ± {std_val:.4f}\")\n", "\n", "print(\"Refinement code is ready. Uncomment the example usage section to run.\")\n"]}, {"cell_type": "code", "execution_count": 34, "id": "2ae1e0b5", "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (1677547362.py, line 1)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[34]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mExplanation:\u001b[39m\n                ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m invalid syntax\n"]}], "source": ["Explanation:\n", "refine_trained_model Function:\n", "Takes the path to your initially trained model, data paths, a configuration dictionary for refinement, and the device.\n", "Loads the Model: It loads the state dictionary from the checkpoint file. It tries to get the model configuration from the checkpoint; if not found, it uses defaults. It then initializes a new SkeletonAwareSpotDetector instance and loads the saved weights.\n", "Prepares Data: It uses your create_skeleton_aware_data_loaders_fixed function to create new data loaders for the refinement process. This ensures consistency and applies the fixes (like pin_memory=False).\n", "Strategy Selection:\n", "evaluate_only: If you just want to get a detailed report or run the inspection on the current best model without changing it, this option loads the model, runs evaluate_skeleton_aware_model, and inspect_skeleton_aware_model. It returns the results.\n", "fine_tune: This is for further training. It sets up a new directory for the refined model.\n", "Fine-Tuning Setup (fine_tune strategy):\n", "Creates a new directory refined_model within your main model_dir.\n", "Allows for potential adjustments like changing the learning rate (new_lr), number of epochs (new_epochs), or batch size/worker settings.\n", "Includes an example of how you might freeze encoder layers (useful if you think the encoder is good but heads need more work).\n", "Sets up a new AdamW optimizer, typically with a lower learning rate than the initial training. It uses filter(lambda p: p.requires_grad, ...) to ensure only unfrozen parameters are optimized.\n", "Sets up a new OneCycleLR scheduler tailored for the fine-tuning run.\n", "Fine-Tuning Loop (fine_tune strategy):\n", "Runs a simplified version of the main training loop using the new optimizer and scheduler.\n", "Saves the best model based on validation loss during this refinement phase to best_refined_model.pth.\n", "Plots and saves the loss curves for the refinement phase.\n", "Final Evaluation: Regardless of the strategy, it performs a final evaluate_skeleton_aware_model and inspect_skeleton_aware_model on the resulting model (either the original loaded one or the newly fine-tuned one).\n", "Returns: A dictionary summarizing the outcome, including the path to the final model file, metrics, and inspection results.\n", "Example Usage:\n", "Shows how to define paths and two example refinement_config dictionaries.\n", "Demonstrates how to call the refine_trained_model function with either configuration.\n", "Shows how to access the results returned by the function.\n", "This code provides a structured way to take your trained model and either get a final, detailed analysis or perform targeted fine-tuning to potentially squeeze out a bit more performance. You can easily modify the refinement_config to experiment with different fine-tuning approaches (e.g., different LRs, freezing different parts of the network, adjusting epochs)."]}, {"cell_type": "code", "execution_count": null, "id": "9e3f5d9c", "metadata": {}, "outputs": [], "source": ["# Cell: Optimized Tiled Inference & Instance Segmentation\n", "import torch\n", "import torch.nn.functional as F\n", "import numpy as np\n", "from tqdm import tqdm\n", "import tifffile\n", "from scipy.ndimage import maximum_filter\n", "from skimage.segmentation import watershed, find_boundaries\n", "from skimage.measure import regionprops\n", "import cv2\n", "import os\n", "\n", "try:\n", "    from torch.amp import autocast  # PyTorch 2.0+\n", "except ImportError:\n", "    from torch.cuda.amp import autocast  # PyTorch < 2.0\n", "\n", "\n", "def skeleton_aware_instance_segmentation(\n", "    semantic,\n", "    sdt_scalar,\n", "    skeleton,\n", "    centroid_map,\n", "    flow,\n", "    min_size=5,\n", "    nms_threshold=0.4,\n", "    flow_refine_factor=0.3\n", "):\n", "    \"\"\"\n", "    Fast and accurate instance segmentation using all model outputs.\n", "    Returns instance labels and a list of detected spots with refined centroids.\n", "    \"\"\"\n", "    H, W = semantic.shape\n", "    binary_mask = (semantic > 0.3).astype(np.uint8)\n", "\n", "    # 1. Seed detection from centroid map\n", "    peaks = (centroid_map > nms_threshold) & (maximum_filter(centroid_map, size=3) == centroid_map)\n", "    centroid_coords = np.column_stack(np.where(peaks))\n", "\n", "    if len(centroid_coords) == 0:\n", "        print(\"No peaks detected above threshold.\")\n", "        return np.zeros((H, W), dtype=np.int32), []\n", "\n", "    # 2. Create seeds for watershed\n", "    seeds = np.zeros((H, W), dtype=np.int32)\n", "    for idx, (y, x) in enumerate(centroid_coords, 1):\n", "        seeds[y, x] = idx\n", "\n", "    # 3. <PERSON><PERSON> using negative SDT\n", "    instance_labels = watershed(-sdt_scalar, seeds, mask=binary_mask)\n", "\n", "    # 4. Relabel: one ID per connected component\n", "    final_labels = np.zeros_like(instance_labels)\n", "    regions = regionprops(instance_labels)\n", "    new_id = 1\n", "    filtered_spots = []\n", "\n", "    for region in regions:\n", "        if region.area < min_size:\n", "            continue\n", "        mask = (instance_labels == region.label)\n", "        final_labels[mask] = new_id\n", "\n", "        # 5. Refine centroid using flow\n", "        cy, cx = region.centroid\n", "        refined_cy, refined_cx = cy, cx\n", "        if flow is not None and flow.shape[0] == 2:\n", "            ys, xs = np.where(mask)\n", "            if len(ys) > 0:\n", "                avg_flow_y = flow[0][ys, xs].mean()\n", "                avg_flow_x = flow[1][ys, xs].mean()\n", "                refined_cy += flow_refine_factor * avg_flow_y\n", "                refined_cx += flow_refine_factor * avg_flow_x\n", "\n", "        # 6. <PERSON><PERSON><PERSON> scores\n", "        sdt_score = np.mean(sdt_scalar[mask])\n", "        centroid_score = np.mean(centroid_map[mask])\n", "        semantic_score = np.mean(semantic[mask])\n", "        combined_score = (sdt_score + centroid_score + semantic_score) / 3.0\n", "\n", "        filtered_spots.append({\n", "            'y': float(refined_cy),\n", "            'x': float(refined_cx),\n", "            'original_y': float(cy),\n", "            'original_x': float(cx),\n", "            'score': float(combined_score),\n", "            'size': int(region.area),\n", "            'sdt_score': float(sdt_score),\n", "            'centroid_score': float(centroid_score),\n", "            'semantic_score': float(semantic_score)\n", "        })\n", "        new_id += 1\n", "\n", "    print(f\"Instance segmentation completed: Found {len(filtered_spots)} spots.\")\n", "    return final_labels, filtered_spots\n", "\n", "\n", "def tile_inference(\n", "    model,\n", "    image_path,\n", "    device='cuda',\n", "    patch_size=256,\n", "    overlap=0.5,\n", "    batch_size=4,\n", "    return_all_outputs=True\n", "):\n", "    \"\"\"\n", "    Fast tiled inference with full model output extraction and instance segmentation.\n", "    Returns all outputs and final instance labels.\n", "    \"\"\"\n", "    # 1. Load and normalize image\n", "    image = tifffile.imread(image_path).astype(np.float32)\n", "    H, W = image.shape\n", "    vmin, vmax = np.percentile(image, (0.5, 99.5))\n", "    image_norm = np.clip((image - vmin) / (vmax - vmin + 1e-8), 0, 1).astype(np.float32)\n", "\n", "    # 2. Set model to eval mode\n", "    model.eval()\n", "    model.to(device)\n", "\n", "    # 3. <PERSON><PERSON> patch stride\n", "    stride = int(patch_size * (1 - overlap))\n", "    if stride <= 0:\n", "        stride = patch_size // 2\n", "\n", "    # 4. Initialize output accumulators\n", "    def init_accumulator():\n", "        return np.zeros((H, W), dtype=np.float32)\n", "\n", "    semantic_out = init_accumulator()\n", "    skeleton_out = init_accumulator()\n", "    centroid_out = init_accumulator()\n", "    sdt_probs_out = np.zeros((11, H, W), dtype=np.float32)  # 11 bins\n", "    flow_out = np.zeros((2, H, W), dtype=np.float32)\n", "    count_map = init_accumulator()\n", "\n", "    # 5. Prepare patch indices\n", "    y_coords = list(range(0, H, stride))\n", "    x_coords = list(range(0, W, stride))\n", "    total_patches = len(y_coords) * len(x_coords)\n", "\n", "    # 6. Batched inference\n", "    with torch.no_grad():\n", "        patch_batch = []\n", "        coords_batch = []\n", "        pbar = tqdm(total=total_patches, desc=\"Tiled Inference\")\n", "\n", "        for y_start in y_coords:\n", "            for x_start in x_coords:\n", "                y_end = min(y_start + patch_size, H)\n", "                x_end = min(x_start + patch_size, W)\n", "\n", "                # Pad if needed\n", "                pad_h = patch_size - (y_end - y_start)\n", "                pad_w = patch_size - (x_end - x_start)\n", "                patch = image_norm[y_start:y_end, x_start:x_end]\n", "                patch = np.pad(patch, ((0, pad_h), (0, pad_w)), mode='reflect')\n", "\n", "                patch_batch.append(patch)\n", "                coords_batch.append((y_start, y_end, x_start, x_end))\n", "\n", "                if len(patch_batch) >= batch_size or (y_start == y_coords[-1] and x_start == x_coords[-1]):\n", "                    # Batch inference\n", "                    patch_tensor = torch.from_numpy(np.stack(patch_batch)[..., None]).permute(0, 3, 1, 2).float().to(device)\n", "                    with autocast(device_type=device.type):\n", "                        outputs = model(patch_tensor)\n", "\n", "                    # Extract and stitch\n", "                    for i, (y_start, y_end, x_start, x_end) in enumerate(coords_batch):\n", "                        # Remove padding\n", "                        h, w = y_end - y_start, x_end - x_start\n", "\n", "                        if return_all_outputs:\n", "                            semantic_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['sem_out'][i, 0]).cpu().numpy()[:h, :w]\n", "                            skeleton_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['skeleton_out'][i, 0]).cpu().numpy()[:h, :w]\n", "                            centroid_out[y_start:y_end, x_start:x_end] += torch.sigmoid(outputs['hm_out'][i, 0]).cpu().numpy()[:h, :w]\n", "                            sdt_probs_out[:, y_start:y_end, x_start:x_end] += F.softmax(outputs['sdt_out'][i], dim=0).cpu().numpy()[:, :h, :w]\n", "                            flow_out[:, y_start:y_end, x_start:x_end] += outputs['flow_out'][i].cpu().numpy()[:, :h, :w]\n", "\n", "                        count_map[y_start:y_end, x_start:x_end] += 1\n", "                    patch_batch = []\n", "                    coords_batch = []\n", "                    pbar.update(len(coords_batch) if not coords_batch else len(coords_batch))\n", "\n", "        pbar.close()\n", "\n", "    # 7. Normalize by count\n", "    eps = 1e-8\n", "    semantic_out /= (count_map + eps)\n", "    skeleton_out /= (count_map + eps)\n", "    centroid_out /= (count_map + eps)\n", "    sdt_probs_out /= (count_map[None, ...] + eps)\n", "    flow_out /= (count_map[None, ...] + eps)\n", "\n", "    # 8. Reconstruct scalar SDT\n", "    bin_centers = (np.arange(11) + 0.5) / 11\n", "    sdt_scalar_out = np.sum(sdt_probs_out * bin_centers[:, None, None], axis=0)\n", "\n", "    # 9. Instance Segmentation\n", "    print(\"Performing instance segmentation...\")\n", "    instance_labels, filtered_spots = skeleton_aware_instance_segmentation(\n", "        semantic=semantic_out,\n", "        sdt_scalar=sdt_scalar_out,\n", "        skeleton=skeleton_out,\n", "        centroid_map=centroid_out,\n", "        flow=flow_out,\n", "        min_size=5,\n", "        nms_threshold=0.4,\n", "        flow_refine_factor=0.3\n", "    )\n", "\n", "    # 10. Prepare results\n", "    results = {\n", "        'instance_labels': instance_labels,\n", "        'spots': filtered_spots,\n", "        'semantic': semantic_out,\n", "        'sdt_scalar': sdt_scalar_out,\n", "        'skeleton': skeleton_out,\n", "        'centroid': centroid_out,\n", "        'flow': flow_out,\n", "        'sdt_probs': sdt_probs_out if return_all_outputs else None\n", "    }\n", "\n", "    print(f\"✅ Inference completed for {os.path.basename(image_path)}. Found {len(filtered_spots)} spots.\")\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "id": "133028fe", "metadata": {}, "outputs": [], "source": ["# --- Example Usage: Load Model & Run Inference ---\n", "import os\n", "from pathlib import Path\n", "\n", "# 1. Configuration\n", "model_path = '/mnt/d/Users/<USER>/Documents/spot_detector_model_sota/best_model.pth'\n", "image_path = '/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/0 Series001 Green--FLUO--FITC_tile_1.tif'\n", "save_dir = Path(image_path).parent.parent / \"inference_results\"\n", "save_dir.mkdir(exist_ok=True)\n", "\n", "device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "print(f\"Using device: {device}\")\n", "\n", "# 2. <PERSON><PERSON> Trained Model\n", "print(f\"Loading model from: {model_path}\")\n", "checkpoint = torch.load(model_path, map_location=device, weights_only=False)\n", "model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)\n", "model.load_state_dict(checkpoint['model_state_dict'])\n", "model.to(device)\n", "model.eval()\n", "\n", "# 3. Run Fast Tiled Inference\n", "print(f\"Running inference on: {Path(image_path).name}\")\n", "results = tile_inference(\n", "    model=model,\n", "    image_path=image_path,\n", "    device=device,\n", "    patch_size=256,\n", "    overlap=0.5,\n", "    batch_size=8,  # Maximize GPU utilization\n", "    return_all_outputs=True\n", ")\n", "\n", "# 4. Print Results\n", "print(f\"✅ Inference completed!\")\n", "print(f\"   Found {len(results['spots'])} spots.\")\n", "print(f\"   Output keys: {list(results.keys())}\")\n", "print(f\"   Instance labels shape: {results['instance_labels'].shape}\")\n", "print(f\"   Semantic mask range: [{results['semantic'].min():.3f}, {results['semantic'].max():.3f}]\")\n", "\n", "# 5. Save Results (Optional)\n", "base_name = Path(image_path).stem\n", "tifffile.imwrite(save_dir / f\"{base_name}_instance_labels.tif\", results['instance_labels'].astype(np.uint16))\n", "np.save(save_dir / f\"{base_name}_spots.npy\", results['spots'])  # Save spot list\n", "print(f\"💾 Results saved to: {save_dir}\")"]}, {"cell_type": "code", "execution_count": null, "id": "ae4aaeb6", "metadata": {}, "outputs": [], "source": ["# # Cell 9: Complete Pipeline with Updated Paths\n", "# import os\n", "# import glob\n", "\n", "# # Set paths\n", "# image_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/images/\"\n", "# mask_dir = \"/mnt/d/Users/<USER>/FISH_spots/2d/synthetic/masks/\"\n", "# image_paths = sorted(glob.glob(os.path.join(image_dir, '*.tif')))\n", "# mask_paths = sorted(glob.glob(os.path.join(mask_dir, '*.tif')))\n", "\n", "# print(f\"Found {len(image_paths)} images and {len(mask_paths)} masks\")\n", "\n", "# # Verify a few paths\n", "# if image_paths and mask_paths:\n", "#     print(\"Sample image path:\", image_paths[0])\n", "#     print(\"Sample mask path:\", mask_paths[0])\n", "    \n", "#     # Check if corresponding files exist\n", "#     base_name = os.path.splitext(os.path.basename(image_paths[0]))[0]\n", "#     expected_mask = os.path.join(mask_dir, base_name + '.tif')\n", "#     if os.path.exists(expected_mask):\n", "#         print(\"✓ Matching mask found for first image\")\n", "#     else:\n", "#         print(\"⚠️  Matching mask not found for first image\")\n", "        \n", "# # Model output directory\n", "# model_dir = '/mnt/d/Users/<USER>/Documents/spot_detector_model/'\n", "# os.makedirs(model_dir, exist_ok=True)\n", "# print(f\"Model will be saved to: {model_dir}\")\n", "\n", "# # Set device\n", "# device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "# print(f\"Using device: {device}\")\n", "\n", "# # Create data loaders\n", "# print(\"Creating skeleton-aware data loaders...\")\n", "# train_loader, val_loader = create_skeleton_aware_data_loaders(\n", "#     image_paths, mask_paths, \n", "#     batch_size=12,  # Increased batch size for speed\n", "#     patch_size=256, \n", "#     num_workers=4  # Reduced workers to avoid overhead\n", "# )\n", "# print(f\"Training samples: {len(train_loader.dataset)}\")\n", "# print(f\"Validation samples: {len(val_loader.dataset)}\")\n", "\n", "# # Initialize model\n", "# print(\"Initializing skeleton-aware model...\")\n", "# model = SkeletonAwareSpotDetector(in_ch=1, base_ch=48)\n", "# print(f\"Model parameters: {sum(p.numel() for p in model.parameters()):,}\")\n", "\n", "# # Train the model\n", "# print(\"Starting skeleton-aware training...\")\n", "# trained_model, train_losses, val_losses = train_skeleton_aware_model_optimized(\n", "#     model, \n", "#     train_loader, \n", "#     val_loader, \n", "#     num_epochs=300,  # Reduced epochs due to faster convergence\n", "#     device=device,\n", "#     model_dir=model_dir\n", "# )\n", "\n", "# # Save final model\n", "# final_model_path = os.path.join(model_dir, 'final_model.pth')\n", "# torch.save({\n", "#     'model_state_dict': trained_model.state_dict(),\n", "#     'model_config': {\n", "#         'in_ch': 1,\n", "#         'base_ch': 48\n", "#     },\n", "#     'train_losses': train_losses,\n", "#     'val_losses': val_losses\n", "# }, final_model_path)\n", "# print(f\"Final model saved to: {final_model_path}\")\n", "\n", "# # Evaluate model\n", "# print(\"Running skeleton-aware evaluation...\")\n", "# eval_metrics = evaluate_skeleton_aware_model(trained_model, val_loader, device)\n", "# print(f\"Evaluation Results:\")\n", "# print(f\"  Precision: {eval_metrics['precision']:.3f} ± {eval_metrics['std_precision']:.3f}\")\n", "# print(f\"  Recall: {eval_metrics['recall']:.3f} ± {eval_metrics['std_recall']:.3f}\")\n", "# print(f\"  F1-Score: {eval_metrics['f1']:.3f} ± {eval_metrics['std_f1']:.3f}\")\n", "# print(f\"  Skeleton IoU: {eval_metrics['skeleton_iou']:.3f} ± {eval_metrics['std_skeleton_iou']:.3f}\")\n", "# print(f\"  SDT MSE: {eval_metrics['sdt_mse']:.5f} ± {eval_metrics['std_sdt_mse']:.5f}\")\n", "\n", "# # Test inference on sample images\n", "# print(\"Testing skeleton-aware inference...\")\n", "# test_images = image_paths[:3]  # Test on first 3 images\n", "# for i, test_path in enumerate(test_images):\n", "#     print(f\"Testing on image {i+1}: {os.path.basename(test_path)}\")\n", "#     # Load test image\n", "#     test_image = tifffile.imread(test_path).astype(np.float32)\n", "#     test_image = test_image / (test_image.max() + 1e-8)\n", "#     # Run inference\n", "#     result = skeleton_aware_inference(\n", "#         trained_model, \n", "#         test_image, \n", "#         device=device,\n", "#         threshold=0.3,\n", "#         min_distance=3,\n", "#         nms_threshold=0.2\n", "#     )\n", "#     # Visualize results\n", "#     plt.figure(figsize=(20, 10))\n", "#     # Original image with spots\n", "#     plt.subplot(2, 3, 1)\n", "#     plt.imshow(test_image, cmap='gray')\n", "#     if len(result['spots']) > 0:\n", "#         spots_arr = np.array([[spot['y'], spot['x']] for spot in result['spots']])\n", "#         plt.scatter(spots_arr[:, 1], spots_arr[:, 0], c='red', s=30, marker='o')\n", "#     plt.title(f\"Detected Spots: {len(result['spots'])}\")\n", "#     plt.axis('off')\n", "#     # Semantic mask\n", "#     plt.subplot(2, 3, 2)\n", "#     plt.imshow(result['semantic'], cmap='viridis')\n", "#     plt.title(\"Semantic Mask\")\n", "#     plt.axis('off')\n", "#     # SDT\n", "#     plt.subplot(2, 3, 3)\n", "#     plt.imshow(result['sdt'], cmap='magma')\n", "#     plt.title(\"Skeleton-Aware Distance Transform\")\n", "#     plt.axis('off')\n", "#     # Skeleton\n", "#     plt.subplot(2, 3, 4)\n", "#     plt.imshow(result['skeleton'], cmap='bone')\n", "#     plt.title(\"Skeleton\")\n", "#     plt.axis('off')\n", "#     # Instance segmentation\n", "#     instance_viz = label2rgb(result['instance_labels'], bg_label=0, alpha=0.7)\n", "#     plt.subplot(2, 3, 5)\n", "#     plt.imshow(instance_viz)\n", "#     plt.title(\"Instance Segmentation\")\n", "#     plt.axis('off')\n", "#     # Save visualization\n", "#     plt.tight_layout()\n", "#     plt.savefig(os.path.join(model_dir, f'test_result_{i+1}.png'), dpi=150)\n", "#     plt.close()\n", "#     print(f\"  Detected {len(result['spots'])} spots\")\n", "\n", "# # Memory cleanup\n", "# torch.cuda.empty_cache()\n", "# print(\"Training completed successfully!\")"]}], "metadata": {"kernelspec": {"display_name": "Model_spot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}